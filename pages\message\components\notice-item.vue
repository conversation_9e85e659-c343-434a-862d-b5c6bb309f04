<template>
  <view>
    <view class="item" @click="handleClick">
      <view class="top">
        <view class="top-left">
          <image src="@/static/image/message/notice.png" />
        </view>
        <view class="top-right">
          <view class="title" :class="{ isRead: item.msgIsRead == 1 }">{{ item.msgTitle }}</view>
          <view class="desc" :class="{ isRead: item.msgIsRead == 1 }">
            <rich-text :nodes="item.msgContent"></rich-text>
          </view>
          <view class="date" :class="{ isRead: item.msgIsRead == 1 }">{{ item.createTime }}</view>
          <view v-if="item.msgIsRead == 0" class="dot"></view>
        </view>
      </view>
      <view class="bottom" :class="{ isRead: item.msgIsRead == 1 }">查看详情</view>
    </view>
    <u-popup :show="show" :closeOnClickOverlay="false" mode="center" round="18rpx">
      <view class="modal">
        <view class="modal-header">
          <view class="header-left">{{ typeName }}车辆申请详情</view>
          <view class="header-right" @click="toMore">
            <view>查看更多</view>
            <uni-icons type="right" color="#fff" size="18" />
          </view>
        </view>
        <view class="modal-content">
          <template v-if="!isDelete && info.createTime">
            <view class="content-info">
              <view class="info-row">
                <view class="row-dot"></view>
                <view class="row-label">店铺名：</view>
                <view class="row-value">{{ info.shopName || '-' }}</view>
              </view>
              <view class="info-row">
                <view class="row-dot"></view>
                <view class="row-label">车牌号：</view>
                <view class="row-value" style="width: 200rpx; flex: none">{{ carNumber }}</view>
                <view class="row-status" v-if="carStatus">{{ carStatus }}</view>
              </view>
              <view class="info-row" v-if="item.msgType == 15">
                <view class="row-dot"></view>
                <view class="row-label">入场时间：</view>
                <view class="row-value">{{ info.inTime || '-' }}</view>
              </view>
              <view class="info-row">
                <view class="row-dot"></view>
                <view class="row-label">申请时间：</view>
                <view class="row-value">{{ info.createTime || '-' }}</view>
              </view>
              <view class="info-row" v-if="item.msgType == 15">
                <view class="row-dot"></view>
                <view class="row-label">审核时间：</view>
                <view class="row-value">{{ info.reviewerTime || '-' }}</view>
              </view>
              <view class="info-row" v-if="item.msgType == 17">
                <view class="row-dot"></view>
                <view class="row-label">有效期至：</view>
                <view class="row-value">{{ info.effectiveDateEnd || '-' }}</view>
              </view>
            </view>
            <view class="content-footer" v-if="item.msgType == 15 && info.status == 0">
              <view class="footer-btn" @click="handleOperation('reject')">拒绝</view>
              <view class="footer-btn active" @click="handleOperation('resolve')">通过</view>
            </view>
          </template>
          <template v-else-if="isDelete">
            <view class="content-delete">该车辆已删除~</view>
          </template>
        </view>
        <view class="modal-close" @click="close">
          <image src="@/static/image/close-white.png" class="close-img" />
        </view>
      </view>
    </u-popup>
    <u-modal
      :show="showModal"
      title="注意"
      :duration="100"
      showCancelButton
      :content="`申请通过后，车辆在${freeTime}分钟内离场免费！`"
      @cancel="
        showModal = false
        show = true
      "
      @confirm="handleConfirm"
    ></u-modal>
    <u-modal
      :show="showModal2"
      title="注意"
      :duration="100"
      showCancelButton
      content="确认要拒绝吗？"
      @cancel="
        showModal2 = false
        show = true
      "
      @confirm="handleConfirm2"
    ></u-modal>
  </view>
</template>

<script>
const app = getApp()

export default {
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    permissions: {
      type: Array,
      default: () => [],
    },
    hasCarPermission: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      app: null,
      marketId: '',
      show: false,
      info: {},
      isDelete: false,
      linshiStatus: {
        0: '申请中',
        1: '已通过',
        2: '已拒绝',
      },
      yuezuStatus: {
        0: '生效中',
        1: '已过期',
        2: '审核中',
      },
      freeTime: 0,
      showModal: false,
      showModal2: false,
    }
  },
  computed: {
    typeName() {
      const msgMap = {
        15: '临时',
        17: '月租',
      }
      return msgMap[this.item.msgType] || ''
    },
    carNumber() {
      if (this.info.carNumber) {
        return this.info.carNumber.slice(0, 2) + '·' + this.info.carNumber.slice(2)
      }
      return '-'
    },
    carStatus() {
      if (this.item.msgType == 15) {
        return this.linshiStatus[this.info.status]
      }
      if (this.item.msgType == 17) {
        return this.yuezuStatus[this.info.status]
      }
      return ''
    },
  },
  mounted() {
    this.app = getApp()
    this.marketId = this.app.globalData.marketId
  },
  methods: {
    close() {
      console.log('guan')
      this.show = false
    },
    getRule() {
      this.$api.getFreeRule({ marketId: this.marketId }).then((res) => {
        this.freeTime = res.data.shopTemporaryCarTime * 60
      })
    },
    toMore() {
      if (this.item.msgType == 15) {
        uni.navigateTo({
          url: '/pages/manage/linshi-car/linshi-car?status=0',
        })
      }
      if (this.item.msgType == 17) {
        uni.navigateTo({
          url: '/pages/manage/linshi-car/long-car',
        })
      }
    },
    handleClick() {
      // 1. 请求已读消息接口
      if (this.item.msgIsRead == 0) {
        this.$api.readMessage({ ids: this.item.id }).then(() => {
          this.item.msgIsRead = 1
        })
      }
      // 判断权限
      if (this.permissions.includes(6)) {
        if (this.hasCarPermission) {
          this.getRule()
          this.show = true
          const params = {
            carId: this.item.keyId,
            carType: this.item.msgType,
          }
          this.$api.getCarDetail(params).then((res) => {
            if (res.msg == '该车辆已删除') {
              this.info = {}
              this.isDelete = true
            } else {
              this.info = res.data || {}
              this.isDelete = false
            }
          })
        } else {
          uni.showToast({
            title: '暂无权限查看，请联系客服开通',
            icon: 'none',
          })
        }
      } else {
        uni.showToast({
          title: '该功能暂不可用，请联系客服开通',
          icon: 'none',
        })
      }
    },
    handleOperation(value) {
      this.show = false
      if (value === 'reject') {
        this.showModal2 = true
      } else {
        this.showModal = true
      }
    },
    handleConfirm() {
      const params = {
        id: this.item.keyId,
        status: 1,
        userId: this.app.globalData.id,
      }
      this.$api.auditCarTemporary(params).then((res) => {
        this.showModal = false
        this.show = false
      })
    },
    handleConfirm2() {
      const params = {
        id: this.item.keyId,
        status: 2,
        userId: this.app.globalData.id,
      }
      this.$api.auditCarTemporary(params).then((res) => {
        this.showModal2 = false
        this.show = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.item {
  background-color: #fff;
  border-radius: 16rpx;
  width: 100%;

  .top {
    padding: 24rpx;
    display: flex;
    .top-left {
      width: 56rpx;
      margin-right: 24rpx;
      image {
        width: 56rpx;
        height: 56rpx;
      }
    }
    .top-right {
      flex: 1;
      overflow: hidden;
      position: relative;

      .title {
        font-size: 32rpx;
        font-weight: 550;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .desc {
        margin-top: 8rpx;
        font-size: 24rpx;
        color: #585d77;
        line-height: 1.5;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .date {
        margin-top: 8rpx;
        color: #7382a9;
        font-size: 24rpx;
      }
      .dot {
        width: 12rpx;
        height: 12rpx;
        background: #ff612a;
        border-radius: 50%;
        position: absolute;
        right: 0;
        top: 0;
      }
    }
  }
  .bottom {
    height: 88rpx;
    margin-top: 8rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #0862f6;
    font-size: 30rpx;
    border-top: 1rpx solid #dddddd;
  }
  .isRead {
    color: #999 !important;
  }
}
.modal {
  width: 656rpx;
  background: #3890f5;
  position: relative;
  box-sizing: border-box;
  padding: 0 16rpx 16rpx;
  border-radius: 16rpx;
  .modal-header {
    height: 112rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header-left {
      font-size: 32rpx;
      font-weight: 550;
      color: #ffffff;
    }
    .header-right {
      display: flex;
      align-items: center;
      color: #ffffff;
      font-size: 30rpx;
      gap: 4rpx;
    }
  }
  .modal-content {
    background-color: #ffffff;
    border-radius: 8rpx;
    padding: 40rpx 23rpx;
    .content-info {
      .info-row {
        display: flex;
        align-items: center;
        font-size: 30rpx;
        height: 45rpx;
        line-height: 45rpx;
        margin-bottom: 24rpx;
        &:last-child {
          margin-bottom: 0;
        }
        .row-dot {
          width: 8rpx;
          height: 8rpx;
          background: #7382a9;
          border-radius: 50%;
          margin-right: 16rpx;
        }
        .row-label {
          color: #7382a9;
          width: 150rpx;
        }
        .row-value {
          flex: 1;
          color: #191e3e;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .row-status {
          border: 1rpx solid #0862f6;
          border-radius: 4rpx;
          padding: 0 10rpx;
          color: #0862f6;
          font-size: 24rpx;
          height: 45rpx;
          line-height: 45rpx;
        }
      }
    }
    .content-footer {
      margin-top: 59rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .footer-btn {
        width: 280rpx;
        height: 96rpx;
        border-radius: 16rpx;
        color: #191e3e;
        border: 1rpx solid #585d77;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        box-sizing: border-box;
        &.active {
          color: #ffffff;
          background: linear-gradient(360deg, #195cf1 0%, #379dff 100%);
          border: none;
        }
      }
    }
    .content-delete {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200rpx;
    }
  }
  .modal-close {
    position: absolute;
    width: 64rpx;
    height: 64rpx;
    left: 50%;
    bottom: -104rpx;
    transform: translateX(-50%);
    .close-img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
