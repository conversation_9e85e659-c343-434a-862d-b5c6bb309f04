<template>
	<view class="block">
		<template v-if="showLogin">
			<image mode="widthFix" class="logo" src="@/static/image/logo.png"></image>
			<text class="logo_tip">您好，欢迎使用慧市助手</text>
			<view class="account">
				<image mode="widthFix" src="@/static/image/account_icon.png"></image>
				<input type="text" class="account_input" placeholder-class="account_input_plac" placeholder="请输入账号" v-model="account" />
				<uni-icons type="clear" class="account_clear" color="#CCCCCC" v-if="account" @click="account = ''"></uni-icons>
			</view>
			<view class="account password">
				<image mode="widthFix" src="@/static/image/password_icon.png"></image>
				<input :password="passwordType" class="account_input" placeholder-class="account_input_plac" placeholder="请输入密码" v-model="password" />
				<uni-icons type="eye-filled" class="account_clear" color="#CCCCCC" v-if="passwordType" @click="passwordType = false"></uni-icons>
				<uni-icons type="eye-slash-filled" class="account_clear" color="#1592E6" v-else @click="passwordType = true"></uni-icons>
			</view>

			<view class="protocol" @click="protocolCheck = !protocolCheck">
				<image mode="widthFix" :src="protocolCheck ? require('@/static/image/radio_line_check.png') : require('@/static/image/radio_line.png')"></image>
				<!-- <uni-icons type="checkbox-filled" color="#1592E6" size="22" v-if="protocolCheck"></uni-icons> -->
				<!-- <uni-icons type="circle" color="#DCDFE6" size="22" v-else></uni-icons> -->
				<view>
					我已阅读并同意
					<text @click.stop="protocol('1')">《用户协议》</text>
					和
					<text @click.stop="protocol('2')">《隐私政策》</text>
				</view>
			</view>
			<button class="login" @click="login">登录</button>
			<text class="back_one_login" @click="oneClickLogin">返回一键登录</text>
			<view class="login_bottom">
				<text>河南元溯数字科技有限公司提供技术支持</text>
				<text>联系电话：0371-********</text>
				<text>豫ICP备********号-7A</text>
			</view>
		</template>
		<w-dialog :visible.sync="showLoginError" title="登陆失败" text="账号或密码错误，请重新输入。" confirmText="重新输入" confirmColor="#1592E6"></w-dialog>
		<w-dialog :visible.sync="showProtocolError" title="注意" text="请先勾选《用户协议》和《隐私政策》。" :showCancel="false"></w-dialog>


	</view>
</template>

<script>
import JSEncrypt from 'jsencrypt'
export default {
	data() {
		return {
			account: '', // wangzhezhi
			password: '', // 111111
			passwordType: true,
			// rememberPassword: [{
			// 	text: '记住密码',
			// 	value: true
			// }],
			protocolCheck: false,
			showLoginError: false,
			showProtocolError: false,
			showLogin: true
		};
	},
	onLoad() {
		// #ifdef H5
		this.showLogin = true;
		this.cancelUpdate();
		// #endif

		// #ifdef APP-PLUS
		// APP端的版本更新检查已移到App.vue中处理
		this.showLogin = true;
		// #endif
	},
	methods: {
		cancelUpdate() {
			if (uni.getStorageSync('phoneLogin')) {
				let phone = uni.getStorageSync('phoneLogin')
				this.$api
					.appOneClickLogin({
						phone: this.encryptionPhone(phone)
					})
					.then((res) => {
						uni.setStorageSync('phoneLogin', phone)
						const app = getApp();
						app.globalData.id = res.data.id;
						app.globalData.token = res.data.token;
						app.globalData.marketId = res.data.marketId;
						app.globalData.marketName = res.data.marketName;
						app.globalData.appPerm = res.data.appPerm;
						
						// 持久化存储用户登录信息，添加时间戳
						uni.setStorageSync('userInfo', JSON.stringify({
							id: res.data.id,
							token: res.data.token,
							marketId: res.data.marketId,
							marketName: res.data.marketName,
							appPerm: res.data.appPerm,
							timestamp: Date.now()
						}));
						app.getUserInfo(() => {
							uni.switchTab({
								url: '/pages/home/<USER>/index'
							});
						});
					})
					.catch(err => {
						// this.oneClickLogin()
					})
			} else{
				// #ifdef APP-PLUS
				// this.oneClickLogin();
				// #endif
			}
		},
		oneClickLogin() {
			// 一键登录
			uni.getProvider({
				service: 'oauth',
				success: (res) => {
					let providers = res.providers;
					for (let i = 0; i < providers.length; i++) {
						if (providers[i].id == 'univerify') {
							// 包含一键登录
							uni.preLogin({
								provider: 'univerify',
								success: () => {
									//预登录成功
									// 显示一键登录选项
									uni.login({
										provider: 'univerify',
										univerifyStyle: {
											// 自定义登录框样式
											//参考`univerifyStyle 数据结构`
											fullScreen: true,
											icon: {
												path: 'static/image/logo.png' // 自定义显示在授权框中的logo，仅支持本地图片 默认显示App logo
											},
											authButton: {
												title: '本机号码一键登录',
												normalColor: '#1a75ff'
											},
											otherLoginButton: {
												title: '其他登录方式'
											},
											privacyTerms: {
												privacyItems: [
													{
														url: 'https://jk.yszs2017.com/hszs-h5/agreement?type=1',
														title: '用户服务协议'
													},
													{
														url: 'https://jk.yszs2017.com/hszs-h5/agreement?type=2',
														title: '隐私政策'
													}
												]
											}
										},
										success: (res) => {
											// 预登录成功
											let { openid, access_token } = res.authResult;
											uniCloud
												.callFunction({
													name: 'get_phone', // 你的云函数名称
													data: {
														appid: '__UNI__F60EFCB',
														access_token, // 客户端一键登录接口返回的access_token
														openid // 客户端一键登录接口返回的openid
													}
												})
												.then((res) => {
													uni.closeAuthView();
													let phone = res.result.phone;
													this.$api
														.appOneClickLogin({
															phone: this.encryptionPhone(phone)
														})
														.then((res) => {
															uni.setStorageSync('phoneLogin', phone)
															const app = getApp();
															app.globalData.id = res.data.id;
															app.globalData.token = res.data.token;
															app.globalData.marketId = res.data.marketId;
															app.globalData.marketName = res.data.marketName;
															app.globalData.appPerm = res.data.appPerm;
															
															// 持久化存储用户登录信息，添加时间戳
															uni.setStorageSync('userInfo', JSON.stringify({
																id: res.data.id,
																token: res.data.token,
																marketId: res.data.marketId,
																marketName: res.data.marketName,
																appPerm: res.data.appPerm,
																timestamp: Date.now()
															}));
															
															app.getUserInfo(() => {
																uni.reLaunch({
																	url: '/pages/home/<USER>/index'
																});
															});
														})
														.catch((err) => {
															this.showLogin = true;
															setTimeout(() => {
																uni.closeAuthView();
															}, 2500);
														});
												})
												.catch((err) => {
													// 处理错误
													uni.showToast({
														title: '手机号获取失败，已为您选择其他登录方式',
														icon: 'none',
														mask: true,
														duration: 2500
													});
													this.showLogin = true;
													setTimeout(() => {
														uni.closeAuthView();
													}, 2500);
												});
										},
										fail: (err) => {
											// 登录失败
											if (err.code == 30002 || err.code == 30003) {
												this.showLogin = true;
												uni.closeAuthView();
											} else {
												uni.showToast({
													title: '手机号获取失败，已为您选择其他登录方式',
													icon: 'none',
													mask: true,
													duration: 2500
												});
												setTimeout(() => {
													this.showLogin = true;
													uni.closeAuthView();
												}, 2500);
											}
										}
									});
								},
								fail: (res) => {
									uni.showToast({
										title: '手机号获取失败，已为您选择其他登录方式',
										icon: 'none',
										mask: true,
										duration: 2500
									});
									this.showLogin = true;
									setTimeout(() => {
										uni.closeAuthView();
									}, 2500);
								}
							});
							return;
						}
					}
					this.showLogin = true;
				}
			});
		},
		encryptionPhone(phone) {
			let encryptor = new JSEncrypt();
			let puKey =
				'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDGZ/JzmTAEObLEV6riPTKidgyvMsx7408koP3EvUpPIeyvA3Z3aBJf5jC6IvNv3QaIviYFgfbRzP6Y2ZZ+RfwDly9oQmiOJxjdjZ8d61/AgiCEDXbLOgBbShB0BEcwjQHY3voJMcc6ON4wDZC4aGiKavdHnLLQynHTttGQqN5Gg3eM72BLtV00qFXpaiKh0FExYAN7SkIuEyJmtM1YpV6BuAAStHOw+kO6iTMtA1TEatRzx9emTYQ6eGu9VLMrpZQcm6kNjuZH81py93w9pl9iewle5phKd6xXSTY7g/5jltCNxniWp3WvWAOV6EKLPyi9Z4oI80XDMGtm92lIUOBzAgMBAAECggEADkDBZhSHIoaSlRBHMmF7FbitSJi5n9zAfr0h/dQrVmv1eM0i4YkUaNc8gOKp4ktagNBIIvUnKgMz9v8KlJ+qbweZXEAJCs08OA5Ic/nIsyel9laRRiAwjl5PuEXmZ1J5436BL8/npAMZ0xHmZ5j4iZkNkpNrNRyV+MOVYnL/JMmEdeMDhVaB8Xd6v9i4XlioZJdqmff0ETjO5GhKb6jg6JYMFamDvsil4nerq/saH2kA5E0HYTDovukOhPi6CGlqPa+rRvCU7t5oaPfrkmVpYzV6+aNonMEiMuHsmRw9unTcMC6hxpnWgVyzcKz/15gQetoed312O1Og/R/Vb58u4QKBgQDN63jdRnKUYq2F2IOZaFNG8uJ9A7U2pG2by7g6ov//NXzHysRsU3DxQQxPMCRE5VAXfXGSpftZJ2fxV4XfOGgzV1tT/uBrDnCTaivgxm2Ux+TmhJ+HAcesypsxDwnxC4whoEeaCAimJT4ssuFIPBJmcTCWntQFm2VIUL9kZcZKpwKBgQD2qKtJzGHtJPz53PJE+srBv3q92td9ceQpA26baY64nIvgbQsj14iiIOFCnsJ0cDnTE5hoVVk9kmwvryWqWGiERbDyK2ZHDdQpmMOHw7qm5la5aXtc9z8r60lYT+JMfYdivsv55TULZWLgYc6d1q8J6jKHcixss1QlcDhQmvMRVQKBgQCatsjguAVup5VocvytTk0DiYnSE+8bVv9jhvSsWbutrCpMknRyT18UZKMy/CDEAubiP0YNwNfQbGMUWYYj6OWhbYkY2u8SgI7ks2i0QWIj3uyEpjCV5yh3EuL3QS1fBiYLPbmOaWrWKjVEsEHDXB8XCQRQppT2gYLfiovCixUpEQKBgQDSaTwFLkZzJ9RtyV8aHN6NvKwV/PNKXbeGjRjzlm5baZ+hCSQxpRE3TNkcHZZAAeQx/UtL5VLFRXzoiH6W++fcvh6d1wvenRtZiKlzzTISYfelHyhPO9M1wfbFUQ/4tDSjtaMHP9GqMQjR5F1/I74ZtSD5jhn7eFbVwQ8pI6UqKQKBgC/B/mRhgV25DYf6WzvIF99wjAzNIOLB/4KTejYOrjA1S3B5OB8D8gXlSj/XlwByI/4mmk+Ph3f8yZg1YbqdiJfI3gFJHkyn1wcgihDgFYmgLlUpb574fsVPiLXdBcXa2ZIDt9VuTUwTVHptlG0TmiBtwuh2cfrL8Td1k3v+JGsI';
			encryptor.setPublicKey(puKey);
			return encryptor.encrypt(phone);
		},
		protocol(type) {
			uni.navigateTo({
				url: '/pages/mine/agreement/index?type=' + type
			});
		},
		login() {
			// uni.navigateTo({
			// 	url: '/pages/home/<USER>/index'
			// })
			// return
			if (!this.protocolCheck) {
				return (this.showProtocolError = true);
			}

			this.$api
				.login({
					account: this.account,
					password: this.password
				})
				.then((res) => {
					// 存储用户信息到globalData
					const app = getApp();
					app.globalData.id = res.data.id;
					app.globalData.token = res.data.token;
					app.globalData.marketId = res.data.marketId;
					app.globalData.marketName = res.data.marketName;
					app.globalData.appPerm = res.data.appPerm;
					
					// 持久化存储用户登录信息，添加时间戳
					uni.setStorageSync('userInfo', JSON.stringify({
						id: res.data.id,
						token: res.data.token,
						marketId: res.data.marketId,
						marketName: res.data.marketName,
						appPerm: res.data.appPerm,
						timestamp: Date.now()
					}));
					
					app.getUserInfo(() => {
						uni.reLaunch({
							url: '/pages/home/<USER>/index'
						});
					});
				})
				.catch(err => {
					this.showLogin = true
				})
		}
	}
};
</script>

<style lang="scss" scoped>
.block {
	width: 100%;
	height: 100%;
	overflow: hidden;
	background: #fff;
	position: relative;
	view,
	text {
		line-height: 1;
	}
	.logo {
		display: block;
		width: 100rpx;
		height: 100rpx;
		// height: 0;
		margin: 108rpx auto 0 auto;
	}
	.logo_tip {
		display: block;
		text-align: center;
		font-size: 34rpx;
		color: #333;
		margin-top: 24rpx;
	}
	.account {
		display: flex;
		align-items: center;
		width: calc(100% - 130rpx);
		height: 60rpx;
		margin: 188rpx 65rpx 0 65rpx;
		border-bottom: 1rpx solid #bfbfbf;
		image {
			display: block;
			width: 28rpx;
			height: 0;
			margin-left: 20rpx;
			margin-right: 32rpx;
		}
		.account_input {
			width: calc(100% - 130rpx);
			height: 55rpx;
			font-size: 28rpx;
			color: #333;
			border: none;
			padding: 0;
			margin: 0;
		}
		.account_input_plac {
			font-size: 28rpx;
			color: #999;
		}
		.account_clear {
			width: 50rpx;
			height: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
	.password {
		margin-top: 50rpx;
	}

	.protocol {
		display: flex;
		align-items: center;
		margin-left: 85rpx;
		margin-top: 60rpx;
		font-size: 28rpx;
		color: #666;
		image {
			display: block;
			width: 26rpx;
			height: 0;
		}
		> view {
			margin-left: 8rpx;
			> text {
				color: #1592e6;
			}
		}
	}
	.login {
		width: calc(100% - 126rpx);
		height: 80rpx;
		margin: 120rpx 63rpx 0 63rpx;
		background: linear-gradient(270deg, #3bc6ff 0%, #1a75ff 100%);
		border-radius: 50px;
		line-height: 80rpx;
		color: #fff;
		font-size: 34rpx;
	}
	.login_bottom {
		height: 100rpx;
		position: absolute;
		left: 0;
		right: 0;
		margin: auto;
		text-align: center;
		bottom: 20rpx;
		font-size: 22rpx;
		color: #666666;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}
	.back_one_login {
		display: block;
		margin-top: 50rpx;
		text-align: center;
		font-size: 32rpx;
		color: #0862f6;
		font-weight: 500;
		padding: 20rpx;
	}
}
</style>
