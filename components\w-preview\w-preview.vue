<template>
  <view class="preivew" v-if="show">
    <view class="indicator" v-if="imgs.length">{{current}} / {{imgs.length}}</view>
    <swiper class="swiper-box" @change="change">
      <swiper-item v-for="(item, index) in imgs" :key="index" @click="handleClick">
        <img :src="item" class="item_img" alt="" @touchstart="touchstart" @touchend="touchend" />
      </swiper-item>
    </swiper>
    
    <view class="pop_menu" v-if="popShow">
      <view class="pm_btn confirm" @click="saveImg">保存图片</view>
      <view class="pm_btn cancel" @click="popShow=false">取消</view>
    </view>
  </view>
</template>

<script>
const app = getApp()
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    imgs: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      current: 1,
      popShow: false,
      timer: null
    }
  },
  methods: {
    change (e) {
      this.current = e.detail.current
    },
    handleClick () {
      this.$emit('close')
    },
    
    // 长按保存
    touchstart() {
      this.timer = setTimeout(() => {
        this.popShow = true;
      }, 1000); // 设置长按的阈值时间，这里是1000毫秒
    },
    touchend () {
      clearTimeout(this.timer);
    },
    saveImg () {
      // #ifdef H5
      this.popShow = false
      console.log('保存图片仅支持APP端操作')
      // #endif
      
      // #ifdef APP-PLUS
      const sto = app.checkPermission('android.permission.READ_EXTERNAL_STORAGE')
      if (!sto) {
        uni.showModal({
          title: '提示',
          content: '请开启相册权限用于将图片保存到本地相册',
          showCancel: false,
          confirmText: '知道了',
          success: () => {
            uni.saveImageToPhotosAlbum({
              filePath: this.imgs[this.current-1],
              success: () => {
                uni.showToast({ title: '保存成功', icon: 'success' })
                this.popShow = false
              },
              fail: () => {
                uni.showToast({ title: '保存失败', icon: 'error'})
              }
            });
          }
        });
      } else {
        uni.saveImageToPhotosAlbum({
          filePath: this.imgs[this.current-1],
          success: () => {
            uni.showToast({ title: '保存成功', icon: 'success' })
            this.popShow = false
          },
          fail: () => {
            uni.showToast({ title: '保存失败', icon: 'error' })
          }
        });
      }
      // #endif
      
    }
    
  }
}
</script>

<style scoped>
.preivew{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .9);
  z-index: 1001;
}
.swiper-box{
  height: 100%;
}
.item_img{
  width: 100%;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.indicator{
  position: absolute;
  top: 44px;
  left: 0;
  width: 100%;
  z-index: 1002;
  background: rgba(0, 0, 0, .5);
  color: #fff;
  padding: 16rpx;
  text-align: center;
}


.pop_menu{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .6);
}
.pm_btn{
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  height: 80rpx;
  line-height: 80rpx;
  background-color: #fff;
  color: #333;
  text-align: center;
  width: 95%;
  border-radius: 10rpx;
  font-size: 30rpx;
}
.confirm{
  bottom: 110rpx;
  font-weight: bold;
}
.cancel{
  bottom: 20rpx;
}
</style>