<template>
  <view class="progress-container">
    <canvas 
      class="progress-canvas" 
      :canvas-id="`progressCanvas${index}`"
      :style="{ width: parsedWidth + 'px', height: parsedHeight + 'px' }">
    </canvas>
  </view>
</template>

<script>
export default {
  props: {
    percent: {
      type: [Number, String],
      default: 0,
      validator: value => value >= 0 && value <= 100
    },
    strokeWidth: {
      type: Number,
      default: 10
    },
    bgColor: {
      type: String,
      default: '#eeeeee'
    },
    progressColor: {
      type: [String, Object],  // 支持字符串或对象格式
      default: '#19be6b',
      validator: value => {
        if (typeof value === 'string') return true
        // 验证渐变配置对象
        return value?.type && ['linear', 'radial'].includes(value.type) &&
         Array.isArray(value.stops) && value.stops.every(stop => 
           stop.offset >= 0 && stop.offset <= 1 && stop.color
         )
      }
    },
    dotColor: {
      type: String,
      default: '#19be6b' // 默认颜色改为进度条颜色以确保可见
    },
    width: {
      type: [Number, String],
      default: 200
    },
    height: {
      type: [Number, String],
      default: 100
    },
    index: {
      type: Number,
      default: 0
    }
  },
  computed: {
    parsedWidth() {
      return this.parseUnit(this.width);
    },
    parsedHeight() {
      return this.parseUnit(this.height);
    }
  },
  watch: {
    percent: {
      immediate: true,
      handler() {
        setTimeout(() => this.drawProgress(), 100)
      }
    },
    progressColor: {
      deep: true,
      handler() {
        setTimeout(() => this.drawProgress(), 100)
      }
    }
  },
  mounted() {
    setTimeout(() => this.drawProgress(), 100)
  },
  methods: {
    createGradient(ctx, width, height) {
      if (typeof this.progressColor === 'string') return this.progressColor
      
      const config = this.progressColor
      let gradient
      
      // 创建线性渐变
      if (config.type === 'linear') {
        const [x0, y0] = this.parseGradientPoint(config.start, width, height)
        const [x1, y1] = this.parseGradientPoint(config.end, width, height)
        gradient = ctx.createLinearGradient(x0, y0, x1, y1)
      }
      // 创建径向渐变
      else if (config.type === 'radial') {
        const [x0, y0] = this.parseGradientPoint(config.start, width, height)
        const [x1, y1] = this.parseGradientPoint(config.end, width, height)
        const r0 = config.r0 || 0
        const r1 = config.r1 || Math.sqrt(width*width + height*height)/2
        gradient = ctx.createRadialGradient(x0, y0, r0, x1, y1, r1)
      }

      // 添加色标
      config.stops.forEach(stop => {
        gradient.addColorStop(stop.offset, stop.color)
      })

      return gradient
    },
    parseGradientPoint(point, width, height) {
      // 处理百分比坐标
      const parseValue = (val, max) => {
        if (typeof val === 'string' && val.endsWith('%')) {
          return parseFloat(val) / 100 * max
        }
        return Number(val)
      }
      
      return [
        parseValue(point[0], width),
        parseValue(point[1], height)
      ]
    },
    parseUnit(value) {
      if (typeof value === 'string') {
        const rpxMatch = value.match(/^(-?\d+(\.\d+)?)rpx$/);
        if (rpxMatch) {
          return uni.upx2px(parseFloat(rpxMatch[1]));
        }
        const pxMatch = value.match(/^(-?\d+(\.\d+)?)px$/);
        if (pxMatch) {
          return parseFloat(pxMatch[1]);
        }
        return parseFloat(value);
      }
      return value;
    },
    drawProgress() {
      const ctx = uni.createCanvasContext(`progressCanvas${this.index}`, this);
      const width = this.parsedWidth;
      const height = this.parsedHeight;
      console.log('Canvas尺寸：', this.parsedWidth, this.parsedHeight, this.strokeWidth)
      const radius = width / 2 - this.strokeWidth;
      const centerX = width / 2;
      const centerY = height - this.strokeWidth / 2; // 调整圆心Y坐标

      // 清空画布
      ctx.clearRect(0, 0, width, height);

      // 绘制背景环
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, Math.PI, 0);
      ctx.setLineWidth(this.strokeWidth);
      ctx.setStrokeStyle(this.bgColor);
      ctx.stroke();
      
      // 创建渐变或使用纯色
      const progressStyle = this.createGradient(ctx, width, height)   
      // 绘制进度环
      const progressAngle = Math.PI * (this.percent / 100);
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, Math.PI, Math.PI + progressAngle);
      ctx.setLineWidth(this.strokeWidth);
      ctx.setStrokeStyle(progressStyle);
      ctx.setLineCap('round');
      ctx.stroke();

      // 绘制末端小圆点
      const dotX = centerX + radius * Math.cos(Math.PI + progressAngle);
      const dotY = centerY + radius * Math.sin(Math.PI + progressAngle);
      // 先画白色描边
      ctx.beginPath()
      ctx.arc(dotX, dotY, this.strokeWidth/2 + 1, 0, 2 * Math.PI) // 半径+1扩大描边区域
      ctx.setStrokeStyle('#ffffff')  // 白色描边
      ctx.setLineWidth(2)            // 描边宽度
      ctx.stroke()
      
      ctx.beginPath();
      ctx.arc(dotX, dotY, this.strokeWidth/2, 0, 2 * Math.PI);
      ctx.setFillStyle(this.dotColor);
      ctx.fill();
      
      ctx.draw();
    }
  }
}
</script>

<style>
.progress-container {
  position: relative;
}
.progress-canvas {
  display: block;
}
</style>