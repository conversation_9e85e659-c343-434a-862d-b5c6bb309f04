<template>
  <view class="ys-cell" @click="itemClick">
    <view class="left">
      <image v-if="img" :src="img" style="width: 80rpx; height: 80rpx;" />
      <view class="content">{{content}}</view>
    </view>
    <view class="right" v-if="showArrow">
      <image src="@/static/image/price/arrow-right.png" style="width: 40rpx; height: 40rpx;" />
    </view>
  </view>
</template>

<script>
  export default {
    name:"ys-cell",
    data() {
      return {
        
      }
    },
    props: {
      img: {
        type: String,
        default: require('@/static/image/price/date.png')
      },
      content: {
        type: String,
        default: '默认标题'
      },
      showArrow: {
        type: Boolean,
        default: true
      },
      index: {
        type: Number,
        default: 0
      }
    },
    methods: {
      itemClick() {
        this.$emit('itemClick', this.index)
      }
    }
  }
</script>

<style lang="scss" scoped>
.ys-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #fff;
  height: 128rpx;
  border-radius: 16rpx;
  box-sizing: border-box;
  .left {
    display: flex;
    align-items: center;
    .content {
      color: #191E3E;
      font-size: 32rpx;
      margin-left: 25rpx;
    }
  }
}
</style>