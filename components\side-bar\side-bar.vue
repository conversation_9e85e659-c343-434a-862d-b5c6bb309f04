<template>
	<view class="sidebar-nav">
		<scroll-view scroll-y="true" class="scroll-view">
			<view 
				v-for="(item, index) in navItems" 
				:key="index" 
				:class="['nav-item', { active: activeIndex === index }]"
				@click="selectItem(index)"
			>
				<view class="indicator" v-if="activeIndex === index"></view>
				<text>{{ item.name }}</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	name: "SidebarNav",
	props: {
		// 可以让父组件传入导航数据
		items: {
			type: Array,
			default: () => [
			]
		},
		// 可以让父组件控制初始选中的索引
		initialActiveIndex: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			activeIndex: this.initialActiveIndex, // 当前激活项的索引
			navItems: this.items // 将 props 转换成内部 data，方便管理（如果不需要父组件传入，可以直接在 data 中定义 navItems）
		};
	},
	watch: {
		// 如果父组件动态改变 items，这里也需要更新
		items(newVal) {
			this.navItems = newVal;
			// 如果需要，可以在这里重置 activeIndex 或进行其他处理
			// this.activeIndex = 0; 
		},
		// 如果父组件动态改变 initialActiveIndex
		initialActiveIndex(newVal) {
			this.activeIndex = newVal;
		}
	},
	methods: {
		selectItem(index) {
			if (this.activeIndex !== index) {
				this.activeIndex = index;
				// 触发一个事件，通知父组件选项发生了变化
				// 传递选中的项的数据给父组件
				this.$emit('change', this.navItems[index], index); 
			}
		}
	}
}
</script>

<style scoped lang="scss">
.sidebar-nav {
	width: 160rpx; 
	height: 100%;
	background-color: #fff; 
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}

.scroll-view {
	flex: 1; 
	height: 0; 
}

.nav-item {
	height: 100rpx;
  line-height: 100rpx;
	font-size: 30rpx; 
	color: #191E3E;
	text-align: center; 
	position: relative;

	.indicator {
		position: absolute;
		left: -4rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 10rpx; 
		height: 40rpx; 
		background-color: #0862F6; 
		border-radius: 0rpx 155rpx 201rpx 0rpx;
	}
}

.nav-item.active {
	background: #EDF0F3;
	font-size: 32rpx;
	font-weight: bold; 
}
</style>