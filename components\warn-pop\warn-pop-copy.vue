<template>
  <uni-popup ref="popup" type="bottom" borderRadius="16rpx 16rpx 0 0" background-color="#fff">
    <view class="pop_ctnt">
      <view class="title">{{opts.typeText}}{{opts.state==='1'?'处理进程':''}}</view>
      
      <view class="form">
        <view class="form_row">
          <view class="fr_label"><text style="color:#dd524d;font-weight:bold;">*</text><text>处理情况</text></view>
          <textarea class="fr_val textarea" v-model="processSituation" :disabled="opts.state==='1'" placeholder="请输入预警处理情况（最多500字）" placeholder-style="color:#9DA8C7;font-size:30rpx;" maxlength="500"></textarea>
        </view>
        
        <view class="form_row upload">
          <view v-if="opts.state==='0'" class="fr_label"><text>处理照片：</text><text class="tip">实际处理照片，1-3张</text></view>
          <view v-else-if="opts.state==='1' && fileList.length>0" class="fr_label"><text>处理照片：</text></view>
          
          <view class="file_list">
            <template v-if="fileList.length>0">
              <template v-if="opts.state==='0'">
                <view class="fl_item" v-for="(item, idx) in fileList" :key="idx">
                  <image class="fli_img" :src="item.url"></image>
                  <view class="fli_cover" @click="handleRemove(item)">删除</view>
                </view>
              </template>
              <template v-else-if="opts.state==='1'">
                <view class="fl_item" v-for="(item, idx) in fileList" :key="idx"  @click="preview(idx)">
                  <image class="fli_img" :src="item.url"></image>
                  <view class="fli_cover">查看原图</view>
                </view>
              </template>
            </template>
            <view class="fl_item btn_upload" v-if="fileList.length<3 && opts.state==='0'" @click="beforeChooseImg">
              <view class="up_bg"></view>
              <uni-icons type="camera-filled" class="ico_camera" color="#191E3E" size="40"></uni-icons>
            </view>
          </view>
        </view>
        
        <template  v-if="opts.state==='1'">
          <view class="form_row manager">
            <text class="fr_label">处理领导：</text>
            <view class="fr_val" v-if="opts.signature"><img :src="opts.signature" class="sign_img" alt="" /></view>
            <view class="fr_val" v-else>{{opts.managerName}}</view>
          </view>
          <view class="form_row">
            <text class="fr_label">处理时间：</text>
            <text class="fr_val" >{{opts.processTime}}</text>
          </view>
        </template>
        
      </view>
      
      <view class="btns" v-if="opts.state==='0'">
        <view class="btn_cancel" @click="cancel">暂不处理</view>
        <view class="btn_confirm" @click="confirm">提交</view>
      </view>
      <template v-else-if="opts.state==='1'">
        <!-- <view class="tip" >{{opts.speedProgress}}</view> -->
        <view class="btn_ok" @click="cancel">我知道了</view>
      </template>
      
    </view>
  </uni-popup>
</template>

<script>
  const app = getApp()
  export default {
    props: {
      opts: {
        type: Object,
        default: () => {
          return {}
        }
      },
      show: false,
    },
    data () {
      return {
        global: app.globalData,
        processSituation: '',
        processPhoto: '',
        fileList: [],
        count: 3, // 剩余可上传的图片数量  最多3张
      }
    },
	watch: {
		show(newVal) {
			if (newVal) this.$refs.popup.open()
		}
	},
    mounted () {
      if (this.show) {
        this.$refs.popup.open()
        this.updateOpts(this.opts) 
      }
    },
    methods: {
      updateOpts (opts) {
        // console.log(opts);
        if (opts.type === '1') {
          this.opts.typeText = '经营巡查异常'
        } else if (opts.type === '2') {
          this.opts.typeText = '设备巡查异常'
        } else if (opts.type === '3') {
          this.opts.typeText = '入场查验异常'
        } else if (opts.type === '4') {
          this.opts.typeText = '检测报告异常'
        } else if (opts.type === '5') {
          this.opts.typeText = '人员双证不全'
        }
        
        if (opts.state==='1') { // 数据回显
          this.processSituation = this.opts.processSituation
          this.processPhoto = ''
          if (opts.processPhoto) {
            this.fileList = (opts.processPhoto.split(',')).map(item => { return {url: item} })
          }
        }
        this.$forceUpdate()
      },
      
      // 选择照片 - 前置权限判断
      beforeChooseImg () {
        // #ifdef H5
        this.chooseImg()
        // #endif
        
        // #ifdef APP-PLUS
        const cam = app.checkPermission('android.permission.CAMERA')
        const sto = app.checkPermission('android.permission.READ_EXTERNAL_STORAGE')
        if (!cam) { // 未授权
          uni.showModal({
            title: '提示',
            content: '请开启摄像头权限用于拍照上传经营情况照片',
            showCancel: false,
            confirmText: '知道了',
            success: () => {
              this.chooseImg()
            }
          });
        } else if (!sto) {
          uni.showModal({
            title: '提示',
            content: '请开启相册权限用于读取相册图片上传经营情况照片',
            showCancel: false,
            confirmText: '知道了',
            success: () => {
              this.chooseImg()
            }
          });
        } else {
          this.chooseImg()
        }
        // #endif
      },
      // 选择照片
      chooseImg () {
        uni.chooseImage({
          count: this.count,
          sourceType: ['camera', 'album'],
          success: (res) => {
            res.tempFiles.map(item => {
              this.$api.uploadFile(item.path, {type: 'market'}).then(res1 => {
                this.count = this.count - 1 // 更新文件数量 最多3张
                
                const tmp = res1.msg.split('/')
              	this.fileList.push({url: res1.msg, name: tmp[tmp.length-1]})
              })
            })
            
            // this.$api.uploadFile(res.tempFilePaths[0], {type: 'market'}).then(res => {
            //   const tmp = res.msg.split('/')
            // 	this.fileList.push({url: res.msg, name: tmp[tmp.length-1]})
            // })
          }
        })
      },
      // 重新上传（删除）
      handleRemove (file) {
        const idx = this.fileList.findIndex(item => { return item.name === file.name })
        this.fileList.splice(idx, 1)
        this.count = this.count+1
      },
      preview (idx) {
        const imgs = this.fileList.map(item => { return item.url })
        uni.previewImage({current: idx, urls: imgs, indicator: 'number'})
      },
      // 取消
      cancel () {
        this.$refs.popup.close()
        uni.navigateBack()
      },
      // 提交
      confirm () {
        if (!this.processSituation) {
          uni.showToast({title: '请输入内容', icon: 'error'})
          return false
        }
        
        let processPhoto = ''
        if (this.fileList.length>0) {
          const tmp = this.fileList.map(item => { return item.url })
          processPhoto = tmp.join(',')
        }
        
        const params = {
          userId: this.global.id,
          warnId: this.opts.id,
          processSituation: this.processSituation,
          processPhoto: processPhoto
        }
        
        this.$api.processWarn(params).then(res => {
          uni.setStorageSync('reload', true)
          uni.navigateBack()
        })
        
      }
    }
  }
</script>

<style scoped>
  view, textarea{
    box-sizing: border-box;
  }
  .pop_ctnt{
    padding: 0 30rpx 30rpx 30rpx;
  }
  .title{
    font-size: 34rpx;
    font-weight: bold;
    padding: 10px 0;
    text-align: center;
    /* border-top: #999 0.5px dashed; */
  }
  .desc{
    margin-top: 30rpx;
    color: #7382A9;
    padding-bottom: 10rpx;
    text-align: center;
  }
  
  .form{
    background-color: #fff;
    border-radius: 16rpx;
    padding: 20rpx 0;
  }
  .form_row{
    margin-bottom: 30rpx;
  }
  .form_row:first-child{
    border: none;
  }
  .form_row.manager{
    display: flex;
  }
  .fr_label{
    /* font-weight: bold; */
    color: #191E3E;
    font-size: 26rpx;
    /* padding-bottom: 16rpx; */
  }
  .fr_val{
    position: relative;
    color: #191E3E;
  }
  .sign_img{
    width: 240rpx;
    height: 100rpx;
  }
  .textarea{
    border: #ddd 1rpx solid;
    border-radius: 16rpx;
    width: 100%;
    height: 150rpx;
    padding: 10rpx;
  }
  .form_row .tip{
    padding: 30rpx 0;
    font-size: 24rpx;
    color: #7382A9;
    padding-left: 20rpx;
    font-weight: normal;
  }
  .comp_upload{
    display: flex;
    flex-flow: row nowrap;
  }
  .btn_upload{
    width: 200rpx;
    height: 200rpx;
    position: relative;
  }
  .up_bg{
    width: 100%;
    height: 100%;
    background: #EDF0F3;
    border-radius: 8rpx;
  }
  .ico_camera{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .file_list{
    display: flex;
    flex-flow: row wrap;
    align-items: flex-start;
  }
  .fl_item{
    width: 200rpx;
    height: 200rpx;
    padding-right: 10rpx;
    /* padding-bottom: 10rpx; */
    position: relative;
    margin-bottom: 10rpx;
  }
  .fli_img{
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
    overflow: hidden;
  }
  .fli_cover{
    position: absolute;
    bottom: 0;
    left: 0;
    width: calc(100% - 10rpx);
    background: #0862F6;
    color: #fff;
    border-bottom-left-radius: 8rpx;
    border-bottom-right-radius: 8rpx;
    text-align: center;
    font-size: 26rpx;
    height: 50rpx;
    line-height: 50rpx;
  }
  
  
  
  .btns{
    margin-top: 20rpx;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
  }
  .btn_cancel, .btn_confirm{
    width: 48%;
    text-align: center;
    border-radius: 10rpx;
    color: #191E3E;
    font-size: 28rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
  }
  .btn_cancel{
    border: #585D77 1rpx solid;
  }
  .btn_confirm{
    background: linear-gradient( 360deg, #195CF1 0%, #379DFF 100%);
    color: #fff;
  }
  .tip{
    padding: 30rpx 0;
    font-size: 24rpx;
    color: #7382A9;
    word-break: break-all;
  }
  .btn_ok{
    background: linear-gradient( 360deg, #195CF1 0%, #379DFF 100%);
    color: #fff;
    text-align: center;
    border-radius: 10rpx;
    font-size: 28rpx;
    height: 80rpx;
    line-height: 80rpx;
    margin-top: 40rpx;
  }
</style>