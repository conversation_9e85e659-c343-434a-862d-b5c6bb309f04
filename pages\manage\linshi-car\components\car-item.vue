<template>
  <view class="car-item">
    <view class="top">
      <view class="top-left">
        <image src="@/static/image/warning/ico_chayan.png" />
      </view>
      <view class="top-right">
        <view class="title">{{ carNumber }}</view>
        <view class="shop">店铺名称：{{ item.shopName }} ({{ carStatusMap[item.carStatus] }})</view>
        <view class="date">入场时间：{{ item.inTime }}</view>
        <view class="date">申请时间：{{ item.createTime }}</view>
        <view class="date" v-if="item.status != 0">审核时间：{{ item.reviewerTime }}</view>
        <view class="date" v-if="item.carStatus == 2 && item.outTime">出场时间：{{ item.outTime }}</view>
      </view>
    </view>

    <template v-if="item.status == 0">
      <view class="bottom">
        <view class="btn reject" @click="handleReject">拒绝</view>
        <view class="btn resolve" @click="handleResolve">通过</view>
      </view>
    </template>
    <template v-if="item.status == 1 && !item.outTime">
      <view class="bottom">
        <view style="text-align: center; width: 100%"
          >已通过，距免费离场时间，还剩<text style="color: #f0510a; margin: 0 4rpx">{{ freeExitTime }}</text></view
        >
      </view>
    </template>

    <view class="tip">已停：{{ parkingDuration }}</view>
    <view class="status-icon">
      <image v-if="item.status == 0" src="@/static/image/auditing.png" />
      <image v-if="item.status == 1" src="@/static/image/resolve.png" />
      <image v-if="item.status == 2" src="@/static/image/reject.png" />
    </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    freeTime: {
      type: [Number, String],
      default: 60,
    },
  },
  data() {
    return {
      currentTime: new Date(),
      timer: null,
      carStatusMap: {
        1: '场内',
        2: '出场',
      },
    }
  },
  computed: {
    carNumber() {
      return this.item.carNumber?.slice(0, 2) + '·' + this.item.carNumber?.slice(2)
    },
    parkingDuration() {
      if (!this.item.inTime) return '0秒'

      let endTime
      // 已离场状态使用出场时间，其他状态使用当前时间
      if (this.item.carStatus == 2 && this.item.outTime) {
        endTime = new Date(this.item.outTime)
      } else {
        endTime = this.currentTime
      }

      const startTime = new Date(this.item.inTime)
      const diffMs = endTime - startTime

      if (diffMs < 0) return '0秒'

      const hours = Math.floor(diffMs / (1000 * 60 * 60))
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diffMs % (1000 * 60)) / 1000)

      // 构建显示字符串，只显示非零的时间单位
      const parts = []
      if (hours > 0) parts.push(`${hours}小时`)
      if (minutes > 0) parts.push(`${minutes}分钟`)
      if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`) // 如果所有单位都为0，至少显示秒

      return parts.join('')
    },
    freeExitTime() {
      if (!this.item.reviewerTime || this.item.status !== 1) return '0分0秒'

      const reviewTime = new Date(this.item.reviewerTime)
      const freeExitTime = new Date(reviewTime.getTime() + this.freeTime * 60 * 1000) // 审核时间 + 1小时
      const diffMs = freeExitTime - this.currentTime

      if (diffMs <= 0) return '0分0秒' // 到0时不显示负数

      const totalSeconds = Math.floor(diffMs / 1000)
      const minutes = Math.floor(totalSeconds / 60)
      const seconds = totalSeconds % 60

      return `${minutes}分${seconds}秒`
    },
  },
  mounted() {
    // 只有非已离场状态才需要启动定时器
    if (this.item.status != 3) {
      this.startTimer()
    }
  },
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    startTimer() {
      this.timer = setInterval(() => {
        this.currentTime = new Date()
      }, 1000)
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    handleResolve() {
      this.$emit('resolve', this.item)
    },
    handleReject() {
      this.$emit('reject', this.item)
    },
  },
}
</script>

<style lang="scss" scoped>
.car-item {
  width: 100%;
  background: #ffffff;
  border-radius: 16rpx;
  color: #191e3e;
  position: relative;

  .top {
    padding: 24rpx;
    display: flex;
    .top-left {
      width: 96rpx;
      margin-right: 24rpx;
      image {
        width: 96rpx;
        height: 96rpx;
      }
    }
    .top-right {
      flex: 1;
      .title {
        font-size: 40rpx;
        font-weight: 550;
        line-height: 60rpx;
      }
      .shop {
        font-size: 26rpx;
        color: #585d77;
        line-height: 39rpx;
        margin-top: 12rpx;
        width: 410rpx;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical; 
      }
      .date {
        color: #7382a9;
        font-size: 26rpx;
        line-height: 39rpx;
        margin-top: 12rpx;
      }
    }
  }
  .bottom {
    border-top: 1rpx solid #dddddd;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      flex: 1;
      height: 48rpx;
      font-weight: 550;
      font-size: 32rpx;
      text-align: center;
      &.reject {
        color: #ff612a;
        border-right: 1rpx solid #dddddd;
      }
      &.resolve {
        color: #0862f6;
      }
    }
  }
  .tip {
    position: absolute;
    right: 0;
    top: 0;
    height: 56rpx;
    line-height: 56rpx;
    padding: 0 8rpx;
    font-size: 24rpx;
    color: #0862f6;
    background: #d4eaff;
    border-radius: 0rpx 16rpx 0rpx 16rpx;
  }
  .status-icon {
    position: absolute;
    right: 32rpx;
    top: 97rpx;
    image {
      width: 104rpx;
      height: 104rpx;
    }
  }
}
</style>
