<template>
  <view class="container">
    <uni-nav-bar fixed statusBar :border="false" right-width="140rpx" height="100rpx" @clickLeft="toBack" @clickRight="rightClick">
      <view class="navbar-title">铺位信息</view>
      <template #left>
        <uni-icons type="left" color="#7382A9" size="20" />
      </template>
      <template #right>
        <view class="right-text">经营上报</view>
      </template>
    </uni-nav-bar>
    <view class="tabs-wrapper">
      <template v-for="(item, index) in tabs">
        <view class="tabs-item" :style="{'--item-color': tabsColor[index] }" :key="index" @click="changeTab(index)">
          <view class="tabs-inner" :class="{active: currentIndex === index}">{{item}}</view>
        </view>
      </template>
    </view>
    <view class="main">
      <view class="count-wrapper" :style="headerColor">
        <image v-if="currentIndex === 1" src="@/static/image/berth/market-green.png" style="width: 48rpx; height: 48rpx;" />
        <image v-else-if="currentIndex === 3" src="@/static/image/berth/market-orange.png" style="width: 48rpx; height: 48rpx;" />
        <image v-else src="@/static/image/berth/market.png" style="width: 48rpx; height: 48rpx;" />
        
        
        <view class="count-title">{{tabTitle[currentIndex]}}</view>
        <view class="count-number">{{merchantNum}}</view>
      </view>
      <scroll-view scroll-y class="scroll-wrapper">
        <template v-for="item in list">
          <view class="scroll-item" :key="item.id">
            <view class="item-header">
              <view class="header-title">{{item.districtName}}</view>
            </view>
            <view class="item-content">
              <template v-if="item.hszsMerchantList.length">
                <template v-for="(merchant, index) in item.hszsMerchantList">
                  <view :key="index" class="content-btn" :class="{online: merchant.businessStatus === '0', offline: merchant.businessStatus === '1'}" @click="itemClick(merchant, item)">
                    <view class="btn-circle"></view>
                    <view>{{merchant.berthNumber}}</view>
                  </view>
                </template>
              </template>
              <template v-else>
                <view class="content-empty">暂无相关信息</view>
              </template>
            </view>
          </view>
        </template>
      </scroll-view>
    </view>

  <view class="modal-wrapper" v-if="showModal">
    <view class="modal-content" :style="{background: `linear-gradient( 180deg, ${merchantStatus.bgColor[0]} 0%, ${merchantStatus.bgColor[1]} 100%)`}">
      <view class="content-header">当前商铺{{merchantStatus.status}}</view>
      <view class="content-main">
        <view class="main-image">
          <image :src="info.gatePhoto? info.gatePhoto: require('@/static/image/berth/default_shop_icon.png')" style="width: 100%; height: 100%;">
        </view>
        <view class="main-title">{{info.districtName}} {{info.berthNum}}</view>
        <view class="main-name">{{info.gateName}}</view>
        <view class="off-reason" v-if="info.businessStatus == '1'">歇业原因：{{info.closeReason}}</view>
        <view class="main-contact" v-if="info.businessStatus==='0'" :class="{noclick: info.businessStatus == '2'}" @click="callHandle">联系TA</view>
      </view>
    </view>
    <view class="modal-close" @click="showModal = false">
      <image src="@/static/image/berth/close.png" style="width: 56rpx; height: 56rpx;" />
    </view>
  </view>
  </view>
</template>

<script>
  const app = getApp()
  export default {
    data() {
      return {
        tabs: ['全部', '营业', '歇业', '空铺'],
        tabsColor: ['', '#0B981E', '#0862F6', '#FF612A'],
        tabTitle: ['市场商铺数', '营业商铺数', '歇业商铺数', '未租商铺数'],
        currentIndex: 0,
        showModal: false,
        list: [],
        info: {}
      }
    },
    computed: {
      headerColor() {
        const colors = [['#195CF1', '#379DFF'], ['#A4D62E', '#19AA14'], ['#195CF1', '#379DFF'], ['#FBB449', '#FC4609']]
        return {background: `linear-gradient( 180deg, ${colors[this.currentIndex][0]} 0%, ${colors[this.currentIndex][1]} 100%)`}
      },
      merchantNum() {
        return this.list.reduce((prev, cur) => {
          return prev + cur.hszsMerchantList.length
        }, 0)
      },
      merchantStatus() {
        const shopStatus = ['正常营业', '已歇业', '未出租']
        const shopBgColors = [['#19AA14', '#A4D62E'], ['#379DFF', '#195CF1'], ['#FC4609', '#FBB449']]
        return { status: shopStatus[this.info.businessStatus], bgColor: shopBgColors[this.info.businessStatus] }
      }
    },
    onLoad(options) {
      const tabIndex = options.type
      if(tabIndex) {
        this.currentIndex = Number(tabIndex)
      }
    },
    onReady(){
      this.changeTab(this.currentIndex)
    },
    methods: {
      getList(district = ''){
        this.$api.getBerthInformation({marketId: app.globalData.marketId, businessStatus: district}).then(res => {
          this.list = res.data
        })
      },
      changeTab(index) {
        this.currentIndex = index
        const district = index > 0 ? index - 1 : ''
        this.getList(district)
      },
      itemClick(merchant, item) {
        this.info = {}
        if(merchant.id) {
          this.$api.getMerchantById({id: merchant.id}).then(res => {
            this.info = res.data
            this.info.districtName = item.districtName
            this.info.berthNum = merchant.berthNumber
            this.showModal = true
          })
        } else {
          this.info = {
            businessStatus: '2',
            gatePhoto: '',
            districtName: item.districtName,
            berthNum: merchant.berthNumber
          }
          this.showModal = true
        }
      },
      callHandle() {
        if(this.info.businessStatus == '2') return  
        
        // #ifdef H5
        uni.makePhoneCall({phoneNumber: this.info.shopPhone})
        // #endif
        
        // #ifdef APP-PLUS
        const callPhone = app.checkPermission('android.permission.CALL_PHONE')
        if (!callPhone) { // 未授权
          uni.showModal({
            title: '提示',
            content: '请开启电话权限用于电话联系巡查人员',
            showCancel: false,
            confirmText: '知道了',
            success: () => {
              uni.makePhoneCall({phoneNumber: this.info.shopPhone})
            }
          });
        } else {
          uni.makePhoneCall({phoneNumber: this.info.shopPhone})
        }
        // #endif
      },
      toBack() {
        uni.navigateBack()
      },
      rightClick() {
        uni.navigateTo({
          url: '/pages/manage/jingying/index'
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 100vh;
  background-color: #F1F3F8;
  .navbar-title {
    flex: 1;
    color: #191E3E;
    line-height: 100rpx;
    font-size: 34rpx;
    text-align: center;
  }
  .right-text {
    font-size: 34rpx;
    color: #191E3E;
  }
  
  .tabs-wrapper {
    display: flex;
    align-items: center;
    height: 110rpx;
    background-color: #fff;
    padding: 32rpx 32rpx 20rpx;
    box-sizing: border-box;
    .tabs-item {
      flex: 1;
      .tabs-inner {
        margin:auto;
        width: 100rpx;
        height: 58rpx;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        color: #7382A9;
        position: relative;
        &::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 12rpx;
          height: 12rpx;
          background-color: var(--item-color);
          border-radius: 50%;
        }
        &.active {
          border-bottom: 4rpx solid #191E3E;
          color: #191E3E;
          // font-size: 34rpx;
          font-weight: 500;
        }     
      }
    }
  }
  .main {
    margin-top: 19rpx;
    padding: 0 24rpx;
    .count-wrapper {
      height: 96rpx;
      background: linear-gradient( 360deg, #195CF1 0%, #379DFF 100%);
      border-radius: 16rpx 16rpx 0rpx 0rpx;
      display: flex;
      align-items: center;
      color: #fff;
      padding: 0 24rpx;
      .count-title {
        font-size: 26rpx;
        opacity: .9;
        margin: 0 24rpx;
      }
      .count-number {
        font-family: Roboto, Roboto;
        font-weight: 500;
        font-size: 48rpx;
      }
    }
    .scroll-wrapper {
      height: calc(100vh - 100rpx - 110rpx - 19rpx - 96rpx - 24rpx - var(--status-bar-height));
      background-color: #fff;
      padding: 0 24rpx;
      box-sizing: border-box;
      
      .scroll-item {
        padding: 32rpx 0 8rpx 0;
        .item-header {
          margin-bottom: 25rpx;
          .header-title {
            height: 36rpx;
            font-family: Source Han Sans;
            font-weight: 700;
            font-size: 36rpx;
            color: #191E3E;
            line-height: 36rpx;
          }
        }
        .item-content {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 18rpx;
          .content-empty {
            grid-column: span 4;
            color: #999;
          }
          .content-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 150rpx;
            height: 72rpx;
            background: #F9E1C3;
            border-radius: 8rpx;
            border: 1rpx solid #F0510A;
            color: #FF612A;
            font-size: 34rpx;
            box-sizing: border-box;
            &.online {
              border-color: #0B981E;
              background-color: #D0F4D7;
              color: #0B981E;
              .btn-circle {
                background-color: #0B981E;
                animation: none;
              }
            }
            &.offline {
              border-color: #0862F6;
              background-color: #D4EAFF;
              color: #0862F6;
              .btn-circle {
                background-color: #0862F6;               
              }
            }
            .btn-circle {
              width: 12rpx;
              height: 12rpx;
              background: #FF612A;
              border-radius: 50%;
              margin-right: 8rpx;
              animation: breathe 2s linear infinite;
            }
          }
        }
      }
    }
  }
  
  .modal-wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, .5);
    z-index: 999;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .modal-content {
      width: 656rpx;
      
      border-radius: 16rpx;
      padding: 16rpx;
      box-sizing: border-box;
      .content-header {
        height: 36rpx;
        line-height: 36rpx;
        font-weight: 700;
        font-size: 36rpx;
        color: #FFFFFF; 
        text-align: center;
        padding: 16rpx 0;
      }
      .content-main {
        position: relative;
        margin-top: 14rpx;
        height: 632rpx;
        background: #FFFFFF;
        border-radius: 8rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        .main-image {
          width: 220rpx;
          height: 220rpx;
          border-radius: 16rpx;
          overflow: hidden;
          background-color: #eee;
          margin-top: 48rpx;
        }
        .main-title {
          margin-top: 40rpx;
          height: 34rpx;
          line-height: 34rpx;
          font-size: 34rpx;
          font-weight: 700; 
          color: #191E3E;
        }
        .main-name {
          margin-top: 24rpx;
          height: 28rpx;
          font-weight: 350;
          font-size: 28rpx;
          color: #7382A9;
          line-height: 28rpx;
        }
        .off-reason {
          width: 507rpx;
          height: 48rpx;
          line-height: 48rpx;
          background: #F1F3F8;
          border-radius: 8rpx;
          margin-top: 24rpx;
          text-align: center;
          font-size: 24rpx;
          color: #585D77;
        }
        .main-contact {
          position: absolute;
          bottom: 40rpx;
          width: 507rpx;
          height: 96rpx;
          line-height: 96rpx;
          text-align: center;
          background: linear-gradient( 360deg, #195CF1 0%, #379DFF 100%);
          border-radius: 16rpx;
          color: #fff;
          font-size: 34rpx;
          &.noclick {
            opacity: .5;
            
          }
        }
      }
    }
    .modal-close {
      margin-top: 50rpx;
      width: 80rpx;
      height: 80rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  
  @keyframes breathe {
    from {
      opacity: 1;
    }
    50% {
      opacity: 0.2;
    }
    to {
      opacity: 1;
    }
  }
}
</style>
