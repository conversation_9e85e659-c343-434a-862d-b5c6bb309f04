import {
  request,
  uploadFile,
  uploadFiles
} from './index.js'

const loginApi = '/api/ys-jk-login'

const hszsApi = '/api/ys-hszs'

const codeApi = '/api/ys-code'
const webApi = '/api/ys-jk-web'


// const carLoginApi = '/api/ys-cart-login'

// const carWebApi = '/api/ys-cart-web'
// const bankApi = 'http://127.0.0.1:4523/m1/4130020-0-default'

export default {
  // 上传文件
  uploadFile: (filePath, formData) => {
    return uploadFile(`${hszsApi}/upload/uploadPictures`, filePath, 'file', formData)
  },
  // 获取新版本
  getNewVer: () => {
    return request('GET', `${hszsApi}/version/getNewVer`)
  },
  // 上传文件
  uploadFiles: (files, formData) => {
    return uploadFiles(`${hszsApi}/file/batchUploadPicture`, files, formData)
  },
  // 查询地区
  findAddress: (data) => {
    return request('GET', `${hszsApi}/ysArea/findChildAreas`, data)
  },
  // 查询服务协议隐私政策
  agreementInfo: (data) => {
    return request('GET', `${hszsApi}/hszsAgreement/getInfoById`, data)
  },
  // 登录
  login: (data) => {
    return request('GET', `${hszsApi}/login/appLogin`, data)
  },
  // 手机号一键登录
  appOneClickLogin: (data) => {
    return request('POST', `${hszsApi}/login/appOneClickLogin`, data)
  },
  // 根据用户id查询信息
  queryMarketUserInfoById: (data) => {
    return request('GET', `${hszsApi}/hszsMarketUser/queryMarketUserInfoById`, data, false)
  },
  // 修改用户信息
  deleteSignature: (data) => {
    return request('GET', `${hszsApi}/hszsMarketUser/deleteSignature`, data)
  },
  // 删除个人签字
  editMarketUserInfo: (data) => {
    return request('POST', `${hszsApi}/hszsMarketUser/editMarketUserInfo`, data)
  },
  // 首页任务数量
  queryMouldsTaskCountByExecuteId: (data) => {
    return request('GET', `${hszsApi}/tbMouldsTask/queryMouldsTaskCountByExecuteId`, data)
  },
  // 首页巡查记录
  selectHomeInspectListPage: (data) => {
    return request('POST', `${hszsApi}/dayInspect/selectListPage`, data)
  },
  // 首页整改记录
  selectRectifyListPage: (data) => {
    return request('POST', `${hszsApi}/dayInspect/selectRectifyListPage`, data)
  },
  // 首页整改记录详情
  getRectifyDetail: (data) => {
    return request('GET', `${hszsApi}/dayInspect/getRectifyDetail`, data)
  },
  // 待巡查任务
  queryMouldsTaskPagelist: (data) => {
    return request('GET', `${hszsApi}/tbMouldsTask/queryMouldsTaskPagelist`, data)
  },
  // 设备待巡查任务
  queryMouldsTaskByEquip: (data) => {
    return request('GET', `${hszsApi}/tbMouldsTask/queryMouldsTaskByEquip`, data)
  },
  // 设备整改任务
  selectRectifyList: (data) => {
    return request('POST', `${hszsApi}/dayInspect/selectRectifyList`, data)
  },
  
  // 待巡查任务详情
  getInspectMould: (data) => {
    return request('GET', `${hszsApi}/dayInspect/getInspectMould`, data)
  },
  // 已巡查详情
  getInspectDetail: (data) => {
    return request('GET', `${hszsApi}/dayInspect/getInspectDetail`, data)
  },
  // 获取巡查进度详情
  getLastRectifyDetail: (data) => {
    return request('GET', `${hszsApi}/dayInspect/getLastRectifyDetail`, data)
  },
  // 联系整改人
  relatePhone: (data) => {
    return request('GET', `${hszsApi}/dayInspect/relatePhone`, data)
  },
  // 判断是否跳转巡查任务页面
  getInspectFlag: (data) => {
    return request('GET', `${hszsApi}/dayInspect/getInspectFlag`, data, false)
  },
  // 保存提交巡查记录
  saveInspect: (data) => {
    return request('POST', `${hszsApi}/dayInspect/saveInspect`, data)
  },
  // 待整改 10  待验收 20  已整改 30
  selectWaitRectification: (data) => {
    return request('POST', `${hszsApi}/dayInspect/selectListPage`, data)
  },
  // 整改记录详情
  getInspectRectifyDetail: (data) => {
    return request('GET', `${hszsApi}/dayInspect/getInspectRectifyDetail`, data)
  },
  // 保存提交整改记录
  saveRectify: (data) => {
    return request('POST', `${hszsApi}/dayInspect/saveRectify`, data)
  },
  // 获取上周日期
  findLasWeekDate: () => {
    return request('GET', `${hszsApi}/weekInspect/findLasWeekDate`, {})
  },
  // 添加周排查
  saveWeek: (data) => {
    return request('POST', `${hszsApi}/weekInspect/save`, data)
  },
  // 周排查报告分页查询
  selectWeekListPage: (data) => {
    return request('POST', `${hszsApi}/weekInspect/selectListPage`, data)
  },
  // 删除周排查
  deleteWeek: (data) => {
    return request('GET', `${hszsApi}/weekInspect/delete`, data)
  },
  // 查询周排查详情
  getWeekInfo: (data) => {
    return request('GET', `${hszsApi}/weekInspect/getInfoById`, data)
  },
  // 获取上月日期
  findLasMonthDate: () => {
    return request('GET', `${hszsApi}/monthInspect/findLasMonthDate`, {})
  },
  // 添加月报告
  saveMonth: (data) => {
    return request('POST', `${hszsApi}/monthInspect/save`, data)
  },
  // 月报告分页查询
  selectMonthListPage: (data) => {
    return request('POST', `${hszsApi}/monthInspect/selectListPage`, data)
  },
  // 删除月报告
  deleteMonth: (data) => {
    return request('GET', `${hszsApi}/monthInspect/delete`, data)
  },
  // 查询月详情
  getMonthInfo: (data) => {
    return request('GET', `${hszsApi}/monthInspect/getInfoById`, data)
  },
  // 获取上年日期
  findLasYearDate: () => {
    return request('GET', `${hszsApi}/yearInspect/findLasYearDate`, {})
  },
  // 添加年总结
  saveYear: (data) => {
    return request('POST', `${hszsApi}/yearInspect/save`, data)
  },
  // 年总结分页查询
  selectYearListPage: (data) => {
    return request('POST', `${hszsApi}/yearInspect/selectListPage`, data)
  },
  // 删除年总结
  deleteYear: (data) => {
    return request('GET', `${hszsApi}/yearInspect/delete`, data)
  },
  // 查询年详情
  getYearInfo: (data) => {
    return request('GET', `${hszsApi}/yearInspect/getInfoById`, data)
  },
	// 预制码 查询预制码状态、类型
	getCode: (data) => {
		return request('GET', `/api/ys-code/getCode`, data, false)
	},
	//查验数据
	entryChectStat: (data) => {
	  return request('GET', `${hszsApi}/hszsEntryInspection/appFindNum`, data, false)
	},
  //近30天入场查验记录
	queryCheckRecoreds: (data) => {
		return request('GET', `${hszsApi}/hszsEntryInspection/appFindEntryInspection`, data)
	},
	//查询车型
	queryCarType: (data) =>{
		return request('GET', `${hszsApi}/hszsVehicleModel/findVehicleModels`, data)
	},
	//查询注释说明详情
	findEntryAnnotateDescribe: (data) =>{
		return request('GET', `${hszsApi}/hszsAnnotateDescribe/findEntryAnnotateDescribe`, data)
	},
	//根据车牌号查店铺
	queryShopByPlateNumber: (data) => {
		return request('GET', `${hszsApi}/hszsVehicle/findHszsMerchantsByPlateNumber`, data)
	},
	//查询商户
	queryShopList: (data) => {
		return request('GET', `${hszsApi}/hszsMerchant/findMerchants`, data)
	},
	//根据商品名称查商品
	queryGoodsName: (data) => {
		return request('GET', `${hszsApi}/hszsGoods/findHszsGoodsByName`, data)
	},
	//查询商品单位
	queryGoodsUnit: (data) =>{
		return request('GET', `${hszsApi}/ysDictItem/findGoodUnits`, data)
	},
	//扫码查询进货台账详情
	scanQueryLedgerDetail: (data) =>{
		return request('GET', `${hszsApi}/hszsEntryInspection/scanCodeFindDetails`, data)
	},
	//查验提交
	entryCheckSubmit: (data) =>{
		return request('POST', `${hszsApi}/hszsEntryInspection/addOrUpdate`, data)
	},
	//入场查验记录详情
	ertryCheckRecordsDetail: (data) =>{
		return request('GET', `${hszsApi}/hszsEntryInspection/findDetails`, data)
	},
  //入场查验记录详情(从预警页面 - 入场查验 - 点击去处理/已处理)
  findEntryInspectionDetails: (data) =>{
  	return request('GET', `${hszsApi}/hszsEntryInspection/findEntryInspectionDetails`, data)
  },
	//查询供应商
	querySupplierList: (data) => {
		return request('GET', `${hszsApi}/hszsSupplier/findHszsSupplier`, data)
	},
	//查询是否有新版本
	hasNewVersion: (data) =>{
		return request('GET', `${hszsApi}/version/hasNewVersion`, data, false)
	},
	// 查询新版本信息
	getNewVer: (data) =>{
		return request('GET', `${hszsApi}/version/getNewVer`, data, false)
	},
	// 用户协议详情
	userAgreeDetail: (data) =>{
		return request('GET', `${hszsApi}/hszsAgreement/userAgreeDetail`, data)
	},
	// 隐私政策详情
	privacyDetail: (data) =>{
		return request('GET', `${hszsApi}/hszsAgreement/privacyDetail`, data)
	},
	//根据车牌号查车型
	queryCarTypeByPlateNumber: (data) =>{
		return request('GET', `${hszsApi}/hszsVehicle/findVehicleModel`, data)
	},
	//根据铺位号查店铺
	queryShopByBerth: (data) =>{
		return request('GET', `${hszsApi}/hszsMerchant/findMerchantByBerthNumber`, data)
	},
	//查询当天需要巡查的全部任务包含待巡查和已巡查
	queryAllInspectTask: (data) =>{
		return request('GET', `${hszsApi}/tbMouldsTask/queryMouldsTasklist`, data)
	},
	//查询当天全部整改任务列表包含待验收和已验收
	queryAllRectificationTask: (data) =>{
		return request('POST', `${hszsApi}/dayInspect/scanRectifyList`, data)
	},
	//今日菜价----查询市场的常用商品
	queryCommonGoodsForMarket: (data) =>{
		return request('GET', `${hszsApi}/hszsUserGoods/findHszsUserGoodsList`, data)
	},
	//今日菜价------查询今日菜价
	queryTodayPrice: (data) =>{
		return request('GET', `${hszsApi}/hszsTodayPrice/findTodayPriceList`, data)
	},
	//查询常用商品
	queryCommonGoodsList: (data) =>{
		return request('GET', `${hszsApi}/hszsChooseGoods/findChooseGoods`, data)
	},
	//选择商品提交
	selectGoodsSubmit: (data) =>{
		return request('POST', `${hszsApi}/hszsChooseGoods/saveChooseGoods`, data)
	},
	//今日菜价提交
	todayPriceSubmit: (data) =>{
		return request('POST', `${hszsApi}/hszsTodayPrice/appSaveTodayPrice`, data)
	},
	//一键清除所有已选商品
	clearAllGoods: (data) =>{
		return request('GET', `${hszsApi}/hszsChooseGoods/cleanChooseGoods`, data)
	},
	//首页----市场自查进度
	marketInspectProgress: (data) =>{
		return request('GET', `${hszsApi}/dayInspect/inspectProcess`, data)
	},
  appStatisticsInspectNum: (data) =>{
  	return request('GET', `${hszsApi}/tbMouldsTask/appStatisticsInspectNum`, data, false)
  },
	//首页-----入场查验---查验批次、商品、商户
	// entryChectStat: (data) =>{
	// 	return request('GET', `${hszsApi}/hszsEntryInspection/appFindNum`, data)
	// },
	//首页-----入场查验---折线图
	entryCheckLineChart: (data) =>{
		return request('GET', `${hszsApi}/hszsEntryInspection/lineChart`, data, false)
	},
	//首页-----检验检测统计
	checkoutTestStat: (data) =>{
		return request('GET', `${hszsApi}/hszsTesting/findTodayTestingStatistics`, data, false)
	},
	//首页-----检验检测-------近七日柱状图
	inspectionTestBarChart: (data) =>{
		return request('GET', `${hszsApi}/hszsTesting/findSevenDayTesting`, data, false)
	},
	//首页-------铺位状态数量营业、打烊、空铺以及商户总数
	marketShopStat: (data) =>{
		return request('GET', `${hszsApi}/hszsBerth/findBerthStatusNum`, data, false)
	},
	//首页-----参考均价
	referenceAvgPrice: (data) =>{
		return request('GET', `${hszsApi}/hszsTodayPrice/findTodayPriceByDate`, data, false)
	},
	//首页-------查询市场员工状态以及数量
	marketStaffInfo: (data) =>{
		return request('GET', `${hszsApi}/hszsMarketUser/findMarketUserStatusNum`, data, false)
	},
	//首页-----巡查数字角标
	inspectNumbermark: (data) =>{
		return request('GET', `${hszsApi}/tbMouldsTask/queryTaskCountByUser`, data)
	},
	//首页------入场查验数据滚动
	entryCheckInfoRoll: (data) =>{
		return request('GET', `${hszsApi}/hszsEntryInspection/findLatestThreeEntry`, data, false)
	},
	//入场查验-----人工查验----商户名称或铺位号查询店铺
	findMerchantsBycondition: (data) =>{
		return request('GET', `${hszsApi}/hszsMerchant/findMerchantsBycondition`, data)
	},
  //入场查验-----人工查验----根据店铺查询车辆最近信息
  findVehicleByShopId: (data) =>{
  	return request('GET', `${hszsApi}/hszsVehicle/findVehicleByShopId`, data)
  },
  //参考均价 查询列表
  getTodayPriceList: (data) => {
    return request('GET', `${hszsApi}/hszsTodayPrice/findTodayPriceByDate`, data)
  },
  //参考均价 查询历史日期
  getPriceDateList: (data) => {
    return request('GET', `${hszsApi}/hszsTodayPrice/findShowDate`, data)
  },
  
  //校验检测 查询检测商品列表
  findTodayTesting: (data) => {
    return request('GET', `${hszsApi}/hszsTesting/findTodayTesting`, data)
  },
  //校验检测 查检测日期
  getTestingDate: (data) => {
    return request('GET', `${hszsApi}/hszsTesting/findTestingDate`, data)
  },
  //校验检测 查看检测报告
  getDetectedData: (data) => {
    return request('GET', `${hszsApi}/hszsTesting/getDetectedData`, data)
  },
  //铺位管理 列表
	getBerthInformation: (data) => {
	  return request('GET', `${hszsApi}/hszsBerth/findBerthInformation`, data)
	},
  //铺位管理 详情
  getMerchantById: (data) => {
    return request('GET', `${hszsApi}/hszsMerchant/findMerchantById`, data)
  },
  
  //员工管理  员工列表
  getMarketUsers: (data) => {
    return request('GET', `${hszsApi}/hszsMarketUser/findAllMarketUsers`, data)
  },
  //员工管理  考勤记录
  getLeaveRecord: (data) => {
    return request('GET', `${hszsApi}/hszsLeave/findLeaveRecord`, data)
  },
  
  // 经营上报 - 查询所有店铺
  findMerchantBycondition: (data) =>{
  	return request('GET', `${hszsApi}/hszsMerchant/findMerchantBycondition`, data)
  },
  // 经营上报 - 提交表单
  reportSave: (data) =>{
  	return request('post', `${hszsApi}/hszsBusinessReport/save`, data)
  },
  // 考勤上报 - 查询市场人员列表
  findAllMarketUsers: (data) =>{
  	return request('get', `${hszsApi}/hszsMarketUser/findAllMarketUsers`, data)
  },
  // 考勤上报 - 请假上报
  leaveSave: (data) =>{
  	return request('post', `${hszsApi}/hszsLeave/save`, data)
  },
  // 考勤上报 - 请假类型
  findAllLeaveType: (data) =>{
  	return request('get', `${hszsApi}/hszsLeaveType/findAllLeaveType`, data)
  },
  
  // 创建店铺 - 添加店铺
  saveTemporaryMerchant: (data) =>{
  	return request('post', `${hszsApi}/hszsMerchant/saveTemporaryMerchant`, data)
  },
  // 检测登记 - 表单提交
  hszsTestingRegistrant: (data) =>{
  	return request('post', `${hszsApi}/hszsTesting/hszsTestingRegistrant`, data)
  },
  // 检测登记 - 获取详情
  findTestingRegistrantDetails: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/findTestingRegistrantDetails`, data)
  },
  // 检测登记 - 获取详情
  findHszsTestingRegistrantDetails: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/findHszsTestingRegistrantDetails`, data)
  },
  // 检测登记 - 取消检测(历史记录进入详情再取消)
  deleteTestingResult: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/deleteTestingResult`, data)
  },
  // 检测登记 - 取消检测(表单提交后查看详情再取消)
  cancel: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/cancel`, data)
  },
  // 检测登记 - 历史记录
  findTodayTesting: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/findTodayTesting`, data)
  },
  // 检测登记 - 历史记录 - 查询日期列表
  // findTestingDate: (data) =>{
  // 	return request('get', `${hszsApi}/hszsTesting/findTestingDate`, data)
  // },
  // 检测登记 - 历史记录 - 查询批次数
  findHszsTestingRegistrantBatchNum: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/findHszsTestingRegistrantBatchNum`, data)
  },
  
  
	//市场自查详情------巡查进度
	inspectProgress: (data) =>{
  	return request('get', `${hszsApi}/dayInspect/inspectProgress`, data)
  },
	//市场自查详情------巡查明细
	inspectProgressDetails: (data) =>{
		return request('get', `${hszsApi}/dayInspect/inspectionDetails`, data)
	},
	//市场自查详情------验收进度
	checkProgress: (data) =>{
		return request('get', `${hszsApi}/dayInspect/acceptanceProgress`, data)
	},
	//市场自查详情------验收详情柱状图
	checkProgressDetails: (data) =>{
		return request('get', `${hszsApi}/dayInspect/acceptanceDetails`, data)
	},
	//市场自查详情---巡查进度---巡查任务分页
	inspectTaskPaging: (data) =>{
		// return request('get', `${hszsApi}/dayInspect/pageFindInspectTask`, data)
		return request('get', `${hszsApi}/dayInspect/pageFindInspectProgress`, data)
	},
	//市场自查详情---巡查进度---巡查任务分页
	checkTaskPaging: (data) =>{
		return request('get', `${hszsApi}/dayInspect/pageAcceptanceTask`, data)
	},
	//市场自查详情---巡查进度---人员详情和进度详情
	staffDetailAndProgress: (data) =>{
		return request('get', `${hszsApi}/dayInspect/findMarketUserDetails`, data)
	},
  //市场自查详情---巡查进度-数据
  appFindInspectNum: (data) =>{
  	return request('get', `${hszsApi}/tbMouldsTask/appFindInspectNum`, data)
  },
  //市场自查详情---巡查进度-详情
  appStatisticsInspectDetailes: (data) =>{
  	return request('get', `${hszsApi}/tbMouldsTask/appStatisticsInspectDetailes`, data)
  },
	//首页----巡查-----待巡查分页
	waitInspectPaging: (data) =>{
		return request('get', `${hszsApi}/dayInspect/pageFindWaitMouldsTask`, data)
	},
	//首页----巡查-----已巡查分页
	alreadyInspectPaging: (data) =>{
		return request('get', `${hszsApi}/dayInspect/pageFindAlreadyMouldsTask`, data)
	},
	//首页----巡查-----已巡查任务详情(验收任务详情)
	alreadyInspectTaskDetail: (data) =>{
		return request('get', `${hszsApi}/dayInspect/findDayInspectRecord`, data)
	},
  
  // 入场查验 - 历史记录
  findHistoryDates: (data) =>{
  	return request('get', `${hszsApi}/hszsEntryInspection/findHistoryDates`, data)
  },
  // 入场查验 - 历史记录 - 按日期查询
  findDateEntryInspection: (data) =>{
  	return request('get', `${hszsApi}/hszsEntryInspection/findDateEntryInspection`, data)
  },
  // 入场查验 - 详情 - 查询检测批次
  findTestListByShopId: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/findTestListByShopId`, data)
  },
  
  // 预警 - 角标
  findWarnNum: (data) =>{
  	return request('get', `${hszsApi}/hszsWarn/findWarnNum`, data, false)
  },  
  // 预警 - 统计
  warnTypeCount: (data) =>{
  	return request('get', `${hszsApi}/hszsWarn/warnTypeCount`, data)
  },
  // 预警 - 列表
  pageFindWarns: (data) =>{
  	return request('get', `${hszsApi}/hszsWarn/pageFindWarns`, data)
  },
  // 预警处理 - 获取检测报告
  findTestReport: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/findTestReport`, data)
  },
  // 预警处理 
  processWarn: (data) =>{
  	return request('get', `${hszsApi}/hszsWarn/processWarn`, data)
  },
  // 查预警机制
  findWarnMechanismByMarketIdAndName: (data) =>{
  	return request('get', `${hszsApi}/hszsWarnMechanism/findWarnMechanismByMarketIdAndName`, data)
  },
  // 管理 - 市场巡查 - 分页
  hszsFindInspect: (data) =>{
  	return request('get', `${hszsApi}/dayInspect/hszsFindInspect`, data)
  },
  // 管理 - 检验检测 - 检测方法
  hszsFindTestMethods: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/hszsFindTestMethods`, data)
  },
  // 管理 - 检验检测 - 分页
  hszsFindTestGoods: (data) =>{
  	return request('get', `${hszsApi}/hszsTesting/hszsFindTestGoods`, data)
  },
  // 管理 - 入场查验 - 分区列表
  findAllDistricts: (data) =>{
  	return request('get', `${hszsApi}/hszsDistrict/findAllDistricts`, data)
  },
  // 管理 - 入场查验 - 分页
  hszsFindEntry: (data) =>{
  	return request('get', `${hszsApi}/hszsEntryInspection/hszsFindEntry`, data)
  },
  // 管理 - 进货台账 - 分页
  hszsFindPurchaseBill: (data) =>{
  	return request('get', `${hszsApi}/ysPurchaseBill/hszsFindPurchaseBill`, data)
  },
  // 管理 - 销货台账 - 分页
  hszsFindSellBill: (data) =>{
  	return request('get', `${hszsApi}/ysSellBill/hszsFindSellBill`, data)
  },
  // 管理 - 进货台账 - 详情
  findPurchaseBillDetails: (data) =>{
  	return request('get', `${hszsApi}/ysPurchaseBill/findPurchaseBillDetails`, data)
  },
  // 管理 - 销货台账 - 详情
  findSellBillDetails: (data) =>{
  	return request('get', `${hszsApi}/ysSellBill/findSellBillDetails`, data)
  },
  // 判断店铺是否在市场内
  shopInMarket: (data) => {
    return request('get', `${hszsApi}/hszsMerchant/judgeIsRelated`, data, false)
  },
  
  // 预制码 - 获取分区码列表 
  findHszsDistrictList: (data) => {
    return request('get', `${hszsApi}/hszsDistrict/findHszsDistrictList`, data)
  },
  // 预制码 - 获取市场
  findMarket: (data) => {
    return request('get', `${hszsApi}/hszsMarket/findMarket`, data)
  },
  // 预制码 - 绑定二维码
  bindQrCode: (data) => {
    return request('post', `${codeApi}/bind`, data)
  },
  getQrCodeApi: (data) => {
    return request('post', `${codeApi}/tbCodesQueue/selectTbCodesPage`, data)
  },
  // 预制码 - 废除预制码
  cancelCode: (data) => {
    return request('get', `${codeApi}/cancelCode`, data)
  },
  // 预制码 - 教程
  codeHelp: (data) => {
    return request('get', '/api/ys-spxxk/jjhtHelpJiaocheng/queryByhszs')
  },
  // 预制码 - 获取设备列表
  equipList: (data) => {
    return request('get', `${hszsApi}/tbInspectEquip/pageFindList`, data)
  },
  
  // 市场内冷库
  findColdList: (data) => request('get', `${hszsApi}/hszsFreezer/findList`, data),
  // 冷库内库位
  findSlotList: (data) => request('get', `${hszsApi}/hszsFreezerSlot/findSlotsByfreezerId`, data),
  // 库位详情
  findSlotDetail: (data) => request('get', `${hszsApi}/hszsFreezerSlot/freezerSlotDetails`, data),
  // 市场统计
  marketStatistics: (data) => request('get', `${hszsApi}/statistics/manageStatistics`, data, false),
  // 折线图
  marketLineChart: (data) => request('get', `${hszsApi}/statistics/lineChart`, data, false),
  // 首页 - 统计市场内库位
  indexSlotNum: (data) => request('get', `${hszsApi}/hszsFreezerSlot/statisticSlotNum`, data, false),
  
  getShopList: (data) => request('get', `${hszsApi}/hszsMerchant/pageList`, data),
  // 日期内未租赁的库位
  findLNotLeasedSlots: (data) => request('get', `${hszsApi}/hszsFreezerSlot/findLNotLeasedSlots`, data),
  // 登记租赁信息
  saveCold: (data) => request('post', `${hszsApi}/hszsFreezerLeaseRecords/save`, data),
  // 查店铺最近一次的租赁记录
  lastLeaseRecord: (data) => request('get', `${hszsApi}/hszsFreezerLeaseRecords/lastLeaseRecord`, data),
  
  // 维保登记 - 查设备
  findMarketEquip: (data) => request('get', `${hszsApi}/tbInspectEquip/findMarketEquip`, data),
  // 维保登记 - 添加
  saveMaintenance: (data) => request('post', `${hszsApi}/hszsMaintenanceRecords/saveOrUpdate`, data),
  // 智慧巡查 - 获取巡查模板类型列表
  getMouldsTypeList: (data) => request('get', `${hszsApi}/tbInspectMouldsType/list`),
  // 设备类型
  getEquipTypeList: () => request('get', `${hszsApi}/tbInspectEquipType/list`),
  // 获取市场权限
  getMarketPermission: (data) => request('get', `${hszsApi}/hszsMarketPermission/findMarketPermission`, data, false),
  // 获取入库记录
  getAuditList: (data) => request('get', `${hszsApi}/hszsFreezerGoods/auditList`, data),
  // 入库详情
  freezerGoodsDetail: (data) => request('GET', `${hszsApi}/hszsFreezerGoods/details`, data),
  // 审核
  freezerGoodsAudit: (data) => request('GET', `${hszsApi}/hszsFreezerGoods/audit`, data),
  // 判断是否有冷库入库的权限
  freezerPermission: (data) => request('GET', `${hszsApi}/hszsMerchant/freezerPermission`, data),
  
  // 消息列表
  getMessageList: (data) => request('GET', `${webApi}/ysMessage/queryMessageHomeHszs`, data),
  // 未读消息个数
  notReadMessageCount: (data) => request('GET', `${webApi}/ysMessage/weiDuXinXiCountHszs`, data, false),
  // 读消息
  readMessage: (data) => request('GET', `${webApi}/ysMessage/readMessage`, data),
  // 全部已读
  readAllMessage: (data) => request('GET', `${webApi}/ysMessage/readAllHszs`, data),
  // 判断登陆人是否有权限审核临时车
  hasCarPermisson: (data) => request('GET', `${hszsApi}/carReview/isReview`, data),
  // 分页查询临时车
  carTemporaryList: (data) => request('GET', `${hszsApi}/carTemporary/appFageList`, data),
  // 临时车审核
  auditCarTemporary: (data) => request('GET', `${hszsApi}/carTemporary/reviewer`, data),
  // 获取停车免费时长规则
  getFreeRule: (data) => request('GET', `${hszsApi}/carBillingRules/findTypeRules`, data),
  // 车辆管理
  carManageList: (data) => request('GET', `${hszsApi}/carInAndOut/appFindPageList`, data),
  // 车辆管理 - 店铺车辆列表
  appFindCarLongs: (data) => request('GET', `${hszsApi}/carLong/appFindCarLongs`, data),
  // 消息 - 查询车辆详情
  getCarDetail: (data) => request('GET', `${webApi}/carTemporary/selectById`, data, false, false)
}