<template>
	<view class="block">
		<text class="head_back" :style="{top: statusBarHeight + 'px'}"></text>
		<view class="container" :style="{'padding-top': statusBarHeight + 'px'}">
			<view class="user" @click="user">
				<view class="user_left">
					<image :src="avatar? avatar: require('@/static/image/avatar.png')"></image>
					<view class="user_text">
						<text>{{ userName }}</text>
						<text>{{ marketName }}</text>
					</view>
				</view>
				<uni-icons type="right" color="#fff" size="22"></uni-icons>
			</view>
			<view class="about" @click="about">
				<image mode="widthFix" src="@/static/image/about.png"></image>
				<text>关于慧市助手</text>
				<uni-icons type="right" class="icon" size="20" color="#999"></uni-icons>
			</view>
		</view>
		<text class="quit" @click="quit">退出登录</text>
	</view>
</template>

<script>
	const app = getApp()
	export default{
		data() {
			return {
				statusBarHeight: 0,
				avatar: '',
				userName: '',
				marketName: ''
			}
		},
		onLoad() {
			this.statusBarHeight = app.globalData.statusBarHeight
		},
		onShow() {
			this.avatar = app.globalData.avatar
			this.userName = app.globalData.userName
			this.marketName = app.globalData.marketName
		},
		methods: {
			user() {
				uni.navigateTo({
					url: '/pages/mine/user/index'
				})
			},
			about() {
				uni.navigateTo({
					url: '/pages/mine/about/index'
				})
			},
			quit() {
				// 使用完全清理方法确保彻底清除所有登录相关数据
				app.clearAllLoginData()
				
				// 显示退出提示
				uni.showToast({
					title: '已退出登录',
					icon: 'success',
					duration: 1500,
					mask: true
				});
				
				// 延迟跳转到登录页面
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/login/index'
					});
				}, 1500);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.block{
		width: 100%;
		height: 100%;
		overflow: hidden;
		position: relative;
		view,text{
			line-height: 1;
		}
		.head_back{
			display: block;
			width: 100%;
			height: 436rpx;
			position: absolute;
			left: 0;
			background: linear-gradient( 44deg, #0060FF 0%, #00B9FF 70%, #00C2FF 100%);
		}
		.container{
			overflow: hidden;
			position: relative;
			z-index: 2;
			.user{
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: calc(100% - 105rpx);
				margin: 135rpx 50rpx 100rpx 55rpx;
				.user_left{
					display: flex;
					align-items: center;
					image{
						display: block;
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
						border: 2rpx solid #fff;
					}
					.user_text{
						display: flex;
						flex-direction: column;
						margin-left: 20rpx;
						color: #fff;
						text{
							font-size: 26rpx;
							opacity: .7;
							&:first-child{
								font-size: 36rpx;
								font-weight: bold;
								margin-bottom: 15rpx;
								opacity: 1;
							}
						}
					}
				}
			}
			.about{
				width: calc(100% - 48rpx);
				margin: 0 24rpx;
				height: 160rpx;
				background: #fff;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				position: relative;
				image{
					display: block;
					width: 74rpx;
					height: 0;
					margin: 0 32rpx;
				}
				text{
					font-size: 34rpx;
					color: #333;
				}
				.icon{
					position: absolute;
					right: 28rpx;
				}
			}
		}
		.quit{
			width: calc(100% - 126rpx);
			height: 80rpx;
			background: #1A75FF;
			position: absolute;
			bottom: 62rpx;
			left: 63rpx;
			border-radius: 50px;
			text-align: center;
			line-height: 80rpx;
			font-size: 34rpx;
			color: #fff;
		}
	}
</style>