<template>
  <view class="long-item">
    <view class="item-header" @click="toggleExpand">
      <view class="header-left">{{ item.shopName }}</view>
      <view class="header-right">
        <view>{{ item.carLongList.length }}辆</view>
        <uni-icons v-if="item.carLongList.length > 1" type="right" size="16" color="#7382A9" :class="{ 'arrow-expanded': isExpanded }" class="expand-arrow" />
      </view>
    </view>
    <view class="item-content">
      <view class="car-item" v-for="(v, index) in displayedCarList" :key="index">
        <image src="@/static/image/default-car.png" class="car-img" />
        <view class="car-info">
          <view class="car-number">{{ carNumber(v) }}</view>
          <view class="car-time">有效期至：{{ v.effectiveDateEnd || '-' }}</view>
          <view class="car-time">最近入场时间：{{ v.maxInTime || '无' }}</view>
        </view>
        <image v-if="v.status == 0" src="@/static/image/zhengchang.png" class="status-img" />
        <image v-if="v.status == 1" src="@/static/image/yiguoqi.png" class="status-img" />
        <image v-if="v.status == 2" src="@/static/image/auditing.png" class="status-img" />
        <view v-if="index != displayedCarList.length - 1" class="item-line"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LongItem',
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isExpanded: false,
    }
  },
  computed: {
    carNumber() {
      return (item) => {
        return item.carNumber?.slice(0, 2) + '·' + item.carNumber?.slice(2)
      }
    },
    displayedCarList() {
      if (!this.item.carLongList || this.item.carLongList.length === 0) {
        return []
      }
      // 如果只有一辆车或者已展开，显示全部
      if (this.item.carLongList.length === 1 || this.isExpanded) {
        return this.item.carLongList
      }
      // 否则只显示第一辆
      return [this.item.carLongList[0]]
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    toggleExpand() {
      // 只有当车辆数量大于1时才允许展开/收起
      if (this.item.carLongList && this.item.carLongList.length > 1) {
        this.isExpanded = !this.isExpanded
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.long-item {
  width: 100%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  .item-header {
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #d4eaff;
    color: #191e3e;
    padding: 0 24rpx;
    .header-left {
      font-size: 32rpx;
      font-weight: 550;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      flex: 1;
    }
    .header-right {
      width: 140rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-size: 30rpx;
      font-weight: 400;
      gap: 16rpx;
      .expand-arrow {
        transition: transform 0.3s ease;
        &.arrow-expanded {
          transform: rotate(90deg);
        }
      }
    }
  }
  .item-header {
    cursor: pointer;
    &:hover {
      background: #c8e0ff;
    }
  }
  .item-content {
    transition: all 0.3s ease;
    .car-item {
      padding: 24rpx;
      display: flex;
      position: relative;
      .car-img {
        width: 96rpx;
        height: 96rpx;
        border-radius: 16rpx;
      }
      .car-info {
        margin-left: 24rpx;
        flex: 1;
        .car-number {
          color: #191e3e;
          font-size: 32rpx;
          font-weight: 550;
          height: 48rpx;
          line-height: 48rpx;
        }
        .car-time {
          color: #7382a9;
          font-size: 24rpx;
          height: 36rpx;
          line-height: 36rpx;
          margin-top: 8rpx;
        }
      }
      .status-img {
        height: 104rpx;
        width: 104rpx;
        position: absolute;
        right: 14rpx;
        top: 50%;
        transform: translateY(-50%);
      }
      .item-line {
        position: absolute;
        bottom: 0;
        left: 24rpx;
        width: calc(100% - 48rpx);
        height: 1rpx;
        background: #dddddd;
      }
    }
  }
}
</style>
