<template>
	<uni-popup
		ref="area" 
		type="bottom"
		@change="change">
		<view class="area_head">
			<text class="area_cancel" @click="cancel">取消</text>
			<text class="area_title">请选择区域</text>
			<text class="area_confirm" @click="confirm">完成</text>
		</view>
		<view class="area_content">
			<view class="area_check">
				<template v-for="(item, index) in areaData">
					<template v-if="index == 0">
						<view :key="item.key" @click="refeshArea(item, index)">
							<text>{{ item.areaName? item.areaName: '请选择' }}</text>
							<text></text>
						</view>
					</template>
					<template v-else>
						<view v-if="item.areaName" :key="item.key" @click="refeshArea(item, index)">
							<text>{{ item.areaName? item.areaName: '请选择' }}</text>
							<text></text>
						</view>
					</template>
				</template>
			</view>
			<scroll-view scroll-y="true" class="area_scroll">
				<view v-for="(item, index) in areaData" :key="item.key">
					<template v-if="index == areaIndex">
						<view v-for="item2 in item.list" :key="item2.id" class="area_item" @click="checkAddress(item2)">
							<text :style="{color: item.id == item2.id? '#1B80ED': '#333'}">{{ item2.areaName }}</text>
							<uni-icons type="checkmarkempty" class="area_icon" color="#1B80ED" size="24" v-if="item.id == item2.id"></uni-icons>
						</view>
					</template>
				</view>
			</scroll-view>
		</view>
	</uni-popup>
</template>

<script>
	export default {
		props: {
			visible: Boolean,
			// echoProvinceName: String,
			// echoProvinceCode: String,
			// echoCityName: String,
			// echoCityCode: String,
			// echoCountyName: String,
			// echoCountyCode: String,
			// echoStreetName: String,
			// echoStreetCode: String,
		},
		data() {
			return {
				areaList: [],
				areaData: [
					{
						key: 'province',
						areaName: '',
						id: '',
						list: []
					},
					{
						key: 'city',
						areaName: '',
						id: '',
						list: []
					},
					{
						key: 'county',
						areaName: '',
						id: '',
						list: []
					},
					{
						key: 'town',
						areaName: '',
						id: '',
						list: []
					}
				],
				areaIndex: 0
			}
		},
		watch: {
			'visible': function(newVal) {
				if (newVal) {
					// if (this.echoProvinceCode && this.areaData[0].list.length == 0) {
					// 	uni.showLoading({
					// 		title: '加载中..',
					// 		mask: true
					// 	})
					// } else if (this.areaData[0].list.length == 0) {
					// 	this.$refs.area.open('bottom')
					// 	this.findAddress()
					// } else {
					// 	this.$refs.area.open('bottom')
					// }
					if (this.areaData[0].list.length == 0) {
						this.$refs.area.open('bottom')
						this.findAddress()
					} else {
						this.$refs.area.open('bottom')
					}
				} else {
					this.$refs.area.close()
				}
			},
			
		},
		methods: {
			change(e) {
				if (!e.show) this.$emit('update:visible', false)
			},
			refeshArea(item, index) {
				this.areaIndex = index
				for (let i = index; i<this.areaData.length; i++) {
					this.areaData[i].areaName = ''
					this.areaData[i].id = ''
					this.areaData[i].list = []
				}
				this.findAddress(index == 0? 100000: this.areaData[index - 1].id)
			},
			findAddress(parentId = 100000) {
				this.$api.findAddress({
					parentId
				})
				.then(res => {
					this.areaData[this.areaIndex].list = res.data
				})
			},
			checkAddress(item) {
				this.areaData[this.areaIndex].areaName = item.areaName
				this.areaData[this.areaIndex].id = item.id
				if (this.areaIndex == 3) {
					this.confirm()
					return
				} else {
					++ this.areaIndex
				}
				this.findAddress(item.id)
			},
			cancel() {
				this.$emit('update:visible', false)
			},
			confirm() {
				let data = []
				for (let i=0; i<this.areaData.length; i++) {
					data.push({
						key: this.areaData[i].key,
						areaName: this.areaData[i].areaName,
						id: this.areaData[i].id
					})
					if (this.areaData[i].list.length != 0 && this.areaData[i].id == '') {
						return uni.showToast({
							title: '请选择完整的地址',
							icon: 'none',
							mask: true
						})
					}
					if (this.areaData[i].areaName == '钓鱼岛') {
						return uni.showToast({
							title: '此地址暂不可选择',
							icon: 'none',
							mask: true
						})
					}
				}
				this.$emit('update:visible', false)
				this.$emit('getArea', data)
			}
		}
		
	}
</script>

<style scoped lang="scss">
	.uni-popup__wrapper{
		width: 100%;
		.area_head{
			width: calc(100% - 48rpx);
			padding: 30rpx 24rpx;
			background: #fff;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-top-left-radius: 16rpx;
			border-top-right-radius: 16rpx;
		}
		.area_cancel{
			font-size: 28rpx;
			color: #aaa;
			padding: 0 10rpx;
		}
		.area_title{
			font-size: 30rpx;
			color: #000;
			font-weight: 500;
			text-align: center;
		}
		.area_confirm{
			font-size: 28rpx;
			color: #1B80ED;
			padding: 0 10rpx;
		}
		.area_content{
			width: 100%;
			height: 800rpx;
			background: #fff;
			.area_check{
				width: calc(100% - 48rpx);
				height: 60rpx;
				padding: 30rpx 24rpx;
				display: flex;
				view{
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					margin-right: 30rpx;
					text{
						&:first-child{
							font-size: 28rpx;
							color: #333;
						}
						&:last-child{
							display: inline-block;
							height: 4rpx;
							background: #1B80ED;
						}
					}
				}
			}
			.area_scroll{
				width: calc(100% - 48rpx);
				height: 650rpx;
				padding: 15rpx 24rpx;
				.area_item{
					width: 100%;
					height: 70rpx;
					display: flex;
					align-items: center;
					position: relative;
					>text{
						font-size: 30rpx;
						color: #333;
					}
					.area_icon{
						position: absolute;
						right: 0;
					}
				}
			}
		}
	}
</style>