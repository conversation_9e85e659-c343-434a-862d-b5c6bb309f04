<template>
	<uni-popup ref="popup" type="center" :mask-click="false">
		<view class="update_content">
			<image mode="widthFix" class="update_back" src="@/static/image/update_back.png"></image>
			<view class="update">
				<text class="title">发现新版本</text>
				<text class="version">V{{ version }}</text>
				<text class="update_content_title">更新内容:</text>
				<view class="update_text">
					<text></text>
					<text>{{ narration }}</text>
				</view>
				<!-- <view class="update_text">
					<text></text>
					<text>全新界面升级，给您更好的视觉体验优化交互，增加紧急问题反馈新增巡查功能</text>
				</view> -->
				<view class="update_btn" v-if="appState == '1'">
					<text @click="update">立即升级</text>
				</view>
				<view class="update_btn" v-else>
					<text class="cancel_update" @click="waitUpdate">暂不升级</text>
					<text @click="update">立即升级</text>
				</view>
			</view>
		</view>
		<uni-popup ref="schedule" :is-mask-click="false" type="bottom" border-radius="10px 10px 0 0">
			<view class="schedule">
				<text class="schedule_title">正在下载中...</text>
				<view class="schedule_box">
					<text :style="{ width: width + '%' }"></text>
				</view>
				<text class="schedule_percentage">{{ width }}%</text>
			</view>
		</uni-popup>
	</uni-popup>
</template>

<script>
	export default {
		props: {
			visible: Boolean,
			appPackage: String,
			appState: String,
			narration: String,
			version: String
		},
		watch: {
			'visible': function(newVal) {
				if (newVal) this.$refs.popup.open('center')
			}
		},
		data() {
			return {
				width: 0
			}
		},
		mounted() {
			
		},
		methods: {
			change(e) {
				if (!e.show) this.$emit('update:visible', false)
			},
			update() {
				this.$refs.schedule.open()
        console.log(this.appPackage, 'url')
				const downloadTask = uni.downloadFile({
					url: this.appPackage,
					success: (res) => {
						if (res.statusCode === 200) {
							uni.saveFile({
								tempFilePath: res.tempFilePath,
								success: res => {
									uni.openDocument({
										filePath: res.savedFilePath,
										success: res => {}
									})
								},
								fail: err => {
									uni.showToast({
										title: '打开安装包失败',
										icon: 'none'
									})
								}
							})
							return
						}
						this.downOver()
						uni.showToast({
							title: '下载失败，请重试',
							icon: 'none'
						})
					},
					fail: err => {
						this.downOver()
						uni.showToast({
							title: '下载失败，请重试',
							icon: 'none'
						})
					}
				})
				downloadTask.onProgressUpdate((res) => {
					this.width = res.progress
					if (res.progress >= 100) {
						this.downOver()
					}
				})
			},
			downOver() {
				this.$refs.schedule.close()
				this.width = 0
			},
			waitUpdate() {
				uni.setStorageSync('update_app', this.version)
				this.$emit('cancel')
				this.$refs.popup.close()
			}
		}
	}
</script>

<style scoped lang="scss">
	/deep/ .uni-popup__wrapper{
		width: 100% !important;
	}
	.update_content{
		width: 620rpx;
		min-height: 700rpx;
		height: auto;
		margin: 0 auto;
		border-radius: 16rpx;
		position: relative;
		background: #fff;
		.update_back{
			width: 653rpx;
			height: 0;
			position: absolute;
			top: -95rpx;
		}
		.update{
			width: 100%;
			height: 100%;
			position: relative;
			z-index: 2;
			display: flex;
			flex-direction: column;
			.title{
				margin-left: 42rpx;
				margin-top: 80rpx;
				font-size: 55rpx;
				color: #333;
				font-weight: bold;
			}
			.version{
				width: 108rpx;
				height: 38rpx;
				background: #3485FF;
				border-radius: 50px;
				margin-left: 42rpx;
				text-align: center;
				line-height: 38rpx;
				font-size: 24rpx;
				color: #fff;
				margin-top: 40rpx;
			}
			.update_content_title{
				font-size: 30rpx;
				color: #999;
				margin-top: 60rpx;
				margin-left: 42rpx;
			}
			.update_text{
				width: calc(100% - 115rpx);
				margin: 30rpx 70rpx 0 45rpx;
				display: flex;
				text{
					&:first-child{
						display: block;
						width: 12rpx;
						min-width: 12rpx;
						height: 12rpx;
						background: #3CA6F6;
						border-radius: 50%;
						margin-right: 14rpx;
						margin-top: 18rpx;
					}
					&:last-child{
						font-size: 30rpx;
						color: #333;
						line-height: 45rpx;
					}
				}
			}
			.update_btn{
				width: calc(100% - 72rpx);
				margin: 68rpx 36rpx 40rpx 36rpx;
				display: flex;
				align-items: center;
				text{
					width: calc(50% - 15rpx);
					height: 90rpx;
					border: 1rpx solid #1A75FF;
					text-align: center;
					line-height: 92rpx;
					font-size: 34rpx;
					border-radius: 50px;
					color: #fff;
					background-color: #1A75FF;
				}
				.cancel_update{
					margin-right: 30rpx;
					background: none;
					color: #1A75FF;
				}
			}
		}
		
	}
	.schedule{
		display: block;
		background: #fff;
		height: 200rpx;
		.schedule_title{
			display: block;
			padding-top: 30rpx;
			font-size: 32rpx;
			color: #333;
			text-align: center;
		}
		.schedule_box{
			width: calc(100% - 60rpx);
			height: 25rpx;
			margin: 30rpx 30rpx 15rpx 30rpx;
			background: #eee;
			border-radius: 50px;
			position: relative;
			text{
				position: absolute;
				left: 0;
				top: 0;
				height: 25rpx;
				width: 25%;
				background: linear-gradient(90deg, #48AFFF 0%, #1A75FF 100%);
				border-radius: 50px;
				transition: all .5s;
			}
		}
		.schedule_percentage{
			display: block;
			text-align: center;
			color: #48AFFF;
		}
	}
</style>