<template>
	<view class="inspect_block">
		<template v-if="newStatus == 'history' && warn && warn.state == '1'">
			<text class="view_back"></text>
			<text class="view" @click="viewResult">查看处理结果</text>
		</template>
		<uni-popup ref="result" type="bottom" border-radius="10px 10px 0 0">
			<view class="scroll_back" v-if="type != 'test'">
				<uni-icons class="close_icon" type="closeempty" size="24" color="#fff" @click="close"></uni-icons>
			</view>
			<scroll-view scroll-y="true" class="my_scroll" v-if="type != 'test'">
				
				<view class="ctnt" v-if="type == 'inspect'">
					<view class="bill">
						<view class="b_title">市场巡查单</view>
						<image src="@/static/image/check_text.png" class="check_text" mode=""></image>
				
						<!--isChange: 1需要整改(不合格)=>有预警  0不需要整改(合格)=>有或没有预警 -->
						<image v-if="isChange === '1'" src="@/static/image/testing/no.png" class="img_type" mode=""></image>
						<template v-else>
							<image v-if="isWarn" src="@/static/image/testing/yes1.png" class="img_type" mode=""></image>
							<image v-else src="@/static/image/testing/yes.png" class="img_type" mode=""></image>
						</template>
				
						<view class="shop">
              <view class="b_row">巡查单号：{{ orderNumber || '--' }}</view>
							<view class="b_row">{{ equipFlag === '0' ? '设备信息' : '店铺信息' }}：{{ showName }}</view>
							<view class="b_row">巡查模板：{{ mouldsName }}</view>
							<!-- <view class="b_row" v-if="changeStatus==='10'">整改期限：{{changeTime}}</view> -->
							<!-- <view class="b_row">备注：{{changeRemark || '--'}}</view> -->
						</view>
				
						<view class="list">
							<view class="l_title">巡查项</view>
							<view class="l_item" v-for="item in itemSet" :key="item.id">
								<view class="li_head">{{ item.name }}</view>
								<view class="li_body">
									<view class="lib_item" v-for="item2 in item.list" :key="item2.contentId">
										<view class="libi_ctnt" @click="open(item2.contentEscription)">{{ item2.contentName }}<uni-icons v-if="item2.contentEscription" type="info" size="20" color="#3985fb" class="icon_info"></uni-icons></view>
										<view class="libi_btm" v-if="item2.result">
											<template v-if="item2.resultType === '1'">
												<text
													:class="['opt_left_item', item2.result === '合格' ? 'qualified' : 'unqualified', isWarn && item2.isWarnProcess === '1' ? 'blue' : '']"
												>
													{{ item2.result }}
												</text>
											</template>
											<view v-else class="item_text">{{ item2.result }}</view>
										</view>
									</view>
								</view>
							</view>
						</view>
				
						<view class="img_list" v-if="imgs.length">
							<view class="il_head">巡查图片</view>
							<view class="il_body">
								<view class="ilb_item" v-for="(item, idx) in imgs" :key="idx" @click="viewImg(idx)">
									<img :src="item" class="ilbi_img" alt="" />
									<view class="ilbi_btn">查看大图</view>
								</view>
							</view>
						</view>
				
						<view class="person">
							<view class="p_title">巡查人员</view>
							<img v-if="seal" :src="seal" class="p_seal" alt="" />
							<view class="p_btm">
								<image class="pb_img" :src="userLogo ? userLogo : require('@/static/image/default_avatar.png')" mode=""></image>
								<view class="pb_rt">
									<text class="pbr_name">{{ creator }}</text>
									<text class="pbr_role">{{ userDuties }}</text>
									<view class="pbr_time">巡查时间：{{ createTime }}</view>
								</view>
							</view>
						</view>
				
						<view class="result inspect_result" v-if="warn && warn.state">
							<text class="title">{{ type == 'test' ? '市场处理情况' : '处理结果' }}</text>
							<template v-if="type != 'test'">
								<view class="result_item">
									<text class="result_item_text">预警类型：</text>
									<text class="result_item_text">
										{{ warn.type == 1 ? '经营巡查异常' : warn.type == 2 ? '设备巡查异常' : warn.type == 3 ? '入场查验不完善' : '检测报告异常' }}
									</text>
								</view>
							</template>
						
							<view class="result_item">
								<text class="result_item_text">处理情况：</text>
								<text class="result_item_text">
									{{ warn.processSituation }}
								</text>
							</view>
							<view class="result_item" v-if="warn.processPhoto">
								<text class="result_item_text">处理图片</text>
								<view class="scroll_x">
									<view class="scroll_x_item" v-for="item in warn.processPhoto.split(',')" :key="item" @click="prevImg(item)">
										<image :src="item"></image>
										<text>查看原图</text>
									</view>
								</view>
							</view>
							<view class="result_item" style="display: flex">
								<text class="result_item_text">处理人：</text>
								<image class="sign" v-if="warn.signature" :src="warn.signature" @click="prevImg(warn.signature)" />
								<text v-else>{{ warn.managerName }}</text>
							</view>
							<view class="result_item">
								<text class="result_item_text">处理时间：</text>
								<text class="result_item_text">{{ warn.processTime }}</text>
							</view>
						</view>
					</view>
					
					<view class="bill_split"></view>
					<image src="@/static/image/bg2.png" class="bg2" mode=""></image>
				</view>
				
				
				<view class="ctnt" v-if="type == 'check'">
					<view class="bill">
						<view class="b_title">入场查验单</view>
						<image src="@/static/image/check_text.png" class="check_text" mode=""></image>
				
						<!-- 预警流程 -->
						<!-- isChange:     1需要整改(不合格)=>有预警  0不需要整改(合格)=>有或没有预警 -->
						<!-- iswarn        1有预警 0无预警 -->
						<!-- iswarnProcess 1预警已处理 0预警未处理 -->
						<template v-if="baseFormData.isWarn">
							<image v-if="baseFormData.isWarnProcess === '1'" src="@/static/image/testing/yes1.png" class="img_type" mode=""></image>
							<image v-else src="@/static/image/testing/no.png" class="img_type" mode=""></image>
						</template>
						<image v-else src="@/static/image/testing/yes.png" class="img_type" mode=""></image>
				
						<view class="shop">
              <view class="b_row">查验单号：{{ baseFormData.orderNumber || '--' }}</view>
							<view class="b_row">店铺信息：{{ baseFormData.shopName }}</view>
							<view class="b_row">门头名称：{{ baseFormData.gateName }}</view>
							<view class="b_row">
								车辆信息：{{ baseFormData.pn1 }}·{{ baseFormData.pn2 }} - {{ baseFormData.cartType === '常用车' ? '常' : '临' }} - {{ baseFormData.vehicleModelName }}
							</view>
							<view class="b_row">供应商：{{ baseFormData.supplierName || '--' }}</view>
						</view>
						<view class="goods">
							<view class="g_row">
								<text class="gr_cell">商品</text>
								<text class="gr_cell">数量</text>
								<text class="gr_cell">单位</text>
							</view>
							<view class="g_row" v-for="(item, index) in baseFormData.hszsEntryInspectionGoodsList" :key="index">
								<text class="gr_cell">{{ item.goodsName }}</text>
								<text class="gr_cell">{{ item.number }}</text>
								<text class="gr_cell">{{ item.goodsUnitText }}</text>
							</view>
						</view>
						<view class="record">
							<view class="r_title" @click="popOpen()">
                查验依据：{{ baseFormData.hszsEntryInspectionVoucherList.length ? '' : '入场查验证照不全' }}
                <uni-icons v-if="baseFormData.annotateDescribe" type="info" size="20" color="#3985fb" class="icon_info" style="float:right;"></uni-icons>
              </view>
							<view class="r_list" v-if="baseFormData.hszsEntryInspectionVoucherList.length">
								<view class="r_item" v-for="(item, index) in baseFormData.hszsEntryInspectionVoucherList" :key="index" @click="previewImg(index)">
									<image :src="item.url"></image>
									<view class="img_btn">查看原图</view>
								</view>
							</view>
							<view class="b_row">备注信息：{{ baseFormData.notes || '--' }}</view>
						</view>
						<view class="other">
							<img v-if="baseFormData.entrySeal" :src="baseFormData.entrySeal" class="p_seal" alt="" />
							<view class="b_row">查验人：{{ baseFormData.creater || '--' }}</view>
							<view class="b_row">入场时间：{{ baseFormData.createTime || '--' }}</view>
						</view>
				
						<template v-if="JSON.stringify(warn) !== '{}'">
							<warn-pop :opts="warn"></warn-pop>
						</template>
					</view>
					<view class="bill_split"></view>
					<image src="@/static/image/bg2.png" class="bg2" mode=""></image>
				</view>
			</scroll-view>
      
			<view class="result" v-if="warn && warn.state && type == 'test'">
				<uni-icons class="result_close_icon" type="closeempty" size="24" color="#666" @click="close"></uni-icons>
				<text class="title">{{ type == 'test' ? '市场处理情况' : '处理结果' }}</text>
				<template v-if="type != 'test'">
					<view class="result_item">
						<text class="result_item_text">预警类型：</text>
						<text class="result_item_text">
							{{ warn.type == 1 ? '经营巡查异常' : warn.type == 2 ? '设备巡查异常' : warn.type == 3 ? '入场查验不完善' : '检测报告异常' }}
						</text>
					</view>
				</template>
			
				<view class="result_item">
					<text class="result_item_text">处理情况：</text>
					<text class="result_item_text">{{ warn.processSituation }}</text>
				</view>
				<view class="result_item" v-if="warn.processPhoto">
					<text class="result_item_text">处理图片</text>
					<view class="scroll_x">
						<view class="scroll_x_item" v-for="item in warn.processPhoto.split(',')" :key="item" @click="prevImg(item)">
							<image :src="item"></image>
							<text>查看原图</text>
						</view>
					</view>
				</view>
				<view class="result_item" style="display: flex">
					<text class="result_item_text">处理人：</text>
					<image class="sign" v-if="warn.signature" :src="warn.signature" @click="prevImg(warn.signature)" />
					<text v-else>{{ warn.managerName }}</text>
				</view>
				<view class="result_item">
					<text class="result_item_text">处理时间：</text>
					<text class="result_item_text">{{ warn.processTime }}</text>
				</view>
			</view>
		</uni-popup>
    
    <uni-popup ref="commentPop" type="center">
    	<view class="pop_box">
    		<view class="pb_title">温馨提示</view>
    		<view class="p_ctnt">{{baseFormData.annotateDescribe}}</view>
    		<view class="p_btn" @click="popClose()">知道了~</view>
    	</view>
    </uni-popup>
	</view>
</template>

<script>
export default {
	props: {
		newStatus: String,
		warn: Object,
		type: String,
		id: String
	},
	data() {
		return {
			showName: '',
			changeStatus: '',
			mouldsName: '',
			changeTime: '',
			isChange: '',
			changeRemark: '',
			itemSet: [],
			creator: '',
			createTime: '',
			userLogo: '',
			userDuties: '',
			phone: '',
			source: '',
			sourceId: '',
			inspectStatus: '',
			equipFlag: '',
			isWarn: '',
			seal: '',
      orderNumber: '',
			noDisplayBottomBtn: true,
			imgs: [],
			baseFormData: {
				id: '', //添加时传空
				shopId: '', //店铺id
        annotateDescribe: '',
				vehicleModelId: '', //车型id
				vehicleModelName: '', //车型名称
				plateNumber: '', //车牌号
				gateName: '', //门头名称
				berthNumber: '', //铺位号
				supplierId: '', //供应商id
				supplierName: '', //供应商名称
				notes: '', //备注
				createBy: '', //操作人id
				type: '', //类型：0-人工，1-扫码
				isWarn: '',
				entrySeal: '',
        orderNumber: '',
				isWarnProcess: '',
				hszsEntryInspectionGoodsList: [], //商品列表
				hszsEntryInspectionVoucherList: [] //进货凭证
			},
		}
	},
	methods: {
		prevImg(url) {
			uni.previewImage({
				current: 0,
				urls: [url]
			});
		},
		viewResult() {
			if (this.type == 'inspect') {
				this.$api.alreadyInspectTaskDetail({ dayInspectId: this.id, status: '' }).then((res) => {
					this.showName = res.data.showName;
					this.changeStatus = res.data.changeStatus;
					this.mouldsName = res.data.mouldsName;
					this.changeTime = res.data.changeTime;
					this.isChange = res.data.isChange;
					this.changeRemark = res.data.changeRemark;
					this.itemSet = res.data.itemSet;
					this.creator = res.data.creator;
					this.createTime = res.data.createTime;
					this.userLogo = res.data.userLogo;
					this.userDuties = res.data.userDuties;
					this.phone = res.data.phone;
					this.equipFlag = res.data.equipFlag;
					this.isWarn = res.data.isWarn;
					// this.warn = res.data.hszsWarn ? res.data.hszsWarn : this.warn;
					this.seal = res.data.inspectSeal;
          this.orderNumber = res.data.orderNumber
					let imgs = [];
					if (res.data.itemSet.length > 0) {
						res.data.itemSet.map((item) => {
							item.list.map((subItem) => {
								if (subItem.url) {
									const urls = subItem.url.split(';');
									imgs = imgs.concat(urls);
								}
							});
						});
					}
					this.imgs = imgs;
					this.$refs.result.open()
				});
			} else if (this.type == 'test') {
				this.$refs.result.open()
			} else if (this.type == 'check') {
				this.$api.findEntryInspectionDetails({ id: this.id }).then((res) => {
					const pn1 = res.data.plateNumber.substring(0, 2);
					const pn2 = res.data.plateNumber.substring(2);
					this.baseFormData = { ...res.data, pn1: pn1, pn2: pn2 };
					this.$refs.result.open()
				});
			}
		},
		open(text) {
			this.$emit('open', text)
		},
		close() {
			this.$refs.result.close()
		},
    popOpen () {
      if (this.baseFormData.annotateDescribe) {
        this.$refs.commentPop.open()
      }
    },
    popClose () {
      this.$refs.commentPop.close()
    },
	}
};
</script>

<style scoped lang="scss">
	.block{
		
	}
	.scroll_back{
		height: 80rpx;
		position: relative;
	}
	.close_icon{
		position: absolute;
		right: 24rpx;
		top: 100rpx;
		z-index: 999;
	}
	.result_close_icon{
		position: absolute;
		right: 24rpx;
		top: 24rpx;
		z-index: 999;
	}
	.my_scroll{
		width: 100%;
		height: calc(100vh - 300rpx);
		background: #fff;
		padding-top: 80rpx;
		background: linear-gradient(360deg, #379dff 0%, #195cf1 100%);
		position: relative;
	}
	.ctnt {
		padding: 0 24rpx 60rpx 24rpx;
		// height: calc(100% - 100rpx);
		position: relative;
		.top {
			height: 80rpx;
			border-radius: 8rpx;
			background: url('@/static/image/bg1.png') no-repeat center;
			background-size: 100% 100%;
			position: absolute;
			top: 0;
			left: 50%;
			width: calc(100% - 60rpx);
			transform: translateX(-50%);
			.top1 {
				width: calc(100% - 90rpx);
				height: 48rpx;
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
				.top2 {
					height: 25rpx;
					border-top-left-radius: 100rpx;
					border-top-right-radius: 100rpx;
					background: linear-gradient(to bottom, #464952, #e3e4e6);
				}
			}
		}
		.bill {
			width: calc(100% - 102rpx);
			height: 100%;
			overflow-y: auto;
			background: #fff;
			margin: 0 auto;
			padding: 60rpx 24rpx 24rpx 24rpx;
			color: #191e3e;
			position: relative;
			.b_title {
				font-size: 50rpx;
				font-family: Source Han Sans, Source Han Sans;
				font-weight: 500;
				text-align: center;
			}
			.check_text {
				display: block;
				width: 323rpx;
				height: 28rpx;
				margin: 20rpx auto 50rpx auto;
			}
			.b_row {
				font-size: 28rpx;
				word-break: break-all;
				padding-bottom: 20rpx;
				// line-height: 60rpx;
			}
			.img_type {
				position: absolute;
				top: 34rpx;
				right: 20rpx;
				width: 100rpx;
				height: 100rpx;
			}
	
			.list {
				padding-top: 20rpx;
				border-top: #666 1rpx dashed;
				overflow: hidden;
				background-color: #fff;
				.l_title {
					color: #191e3e;
					font-size: 30rpx;
					font-weight: bold;
				}
				.l_item {
					border: #ddd 1px solid;
					border-radius: 16rpx;
					margin-top: 20rpx;
					.li_head {
						border-top-left-radius: 16rpx;
						border-top-right-radius: 16rpx;
						background-color: #edf0f3;
						// padding: 24rpx 0 24rpx 24rpx;
						padding: 20rpx 0 20rpx 20rpx;
						font-size: 28rpx;
						font-weight: bold;
						color: #2a3b48;
					}
					.li_body {
						padding: 20rpx;
					}
					.lib_item {
						padding: 20rpx 0;
						color: #585d77;
						font-size: 26rpx;
						line-height: 45rpx;
						border-bottom: #ddd 1rpx solid;
						display: flex;
						flex-flow: row nowrap;
						.libi_ctnt {
							width: calc(100% - 90rpx);
							// flex: 1;
							// display: flex;
							// flex-flow: row nowrap;
              .icon_info{
                display: inline-block;
                padding: 0 8rpx 0 8rpx;
                transform:translateY(6rpx);
              }
						}
						.libi_btm {
							width: 30%;
							font-size: 26rpx;
							color: #3985fb;
							position: relative;
							.opt_left_item {
								width: 100rpx;
								text-align: center;
								height: 50rpx;
								line-height: 50rpx;
								border-radius: 25rpx;
								display: block;
								font-size: 26rpx;
								position: absolute;
								top: 50%;
								right: 0;
								transform: translateY(-50%);
							}
							.qualified {
								background: #d0f4d7;
								color: #0b981e;
							}
							.qualified.blue {
								background: #d4eaff;
								color: #0862f6;
							}
							.unqualified {
								background: #f9e1c3;
								color: #f0510a;
							}
							.item_text {
								text-align: right;
								line-height: normal;
							}
							.libib_split {
								// float: right;
								// display: inline-block;
								width: 2rpx;
								height: 32rpx;
								background: #ebebeb;
								margin: 7rpx 30rpx 0 0rpx;
							}
							// .libib_rt{
							//   // display: inline-block;
							//   float: right;
							// }
							.libib_rt.btn {
								width: 130rpx;
								font-size: 26rpx;
								text-align: center;
								color: #0862f6;
							}
						}
					}
					.lib_item:first-child {
						padding-top: 0;
					}
					.lib_item:last-child {
						padding-bottom: 0;
						border-bottom: 0;
					}
				}
			}
			.img_list {
				margin: 20rpx 0;
				.il_head {
					font-size: 30rpx;
					font-weight: bold;
					padding: 20rpx 0;
					border-top: #999 1rpx dashed;
				}
				.il_body {
					display: flex;
					flex-flow: row wrap;
					gap: 20rpx;
					.ilb_item {
						position: relative;
						border-radius: 8rpx;
						overflow: hidden;
						.ilbi_img {
							display: block;
							width: 160rpx;
							height: 160rpx;
						}
						.ilbi_btn {
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
							padding: 10rpx 0;
							background-color: #0862f6;
							color: #fff;
							font-size: 26rpx;
							text-align: center;
							line-height: 1;
						}
					}
				}
			}
			.person {
				border-top: #333 1rpx dashed;
				margin-top: 20rpx;
				padding-top: 20rpx;
				background-color: #fff;
				position: relative;
				.p_seal {
					width: 160rpx;
					height: 160rpx;
					border-radius: 50%;
					position: absolute;
					top: 50%;
					right: 0;
					transform: translateY(calc(-50% + 10rpx));
					background: transparent;
				}
				.p_title {
					color: #191e3e;
					font-size: 30rpx;
					font-weight: bold;
				}
				.p_btm {
					display: flex;
					flex-flow: row nowrap;
					margin-top: 20rpx;
					.pb_img {
						width: 100rpx;
						height: 100rpx;
						border-radius: 50%;
					}
					.pb_rt {
						flex: 1;
						padding: 0 20rpx;
						.pbr_name {
							display: inline-block;
							font-size: 32rpx;
							color: #191e3e;
							font-weight: bold;
							vertical-align: middle;
							line-height: 1;
						}
						.pbr_role {
							display: inline-block;
							color: #7382a9;
							font-size: 22rpx;
							border: #7382a9 1rpx solid;
							border-radius: 4rpx;
							line-height: 1;
							padding: 2rpx 10rpx;
							margin-left: 20rpx;
						}
						.pbr_time {
							color: #7382a9;
							font-size: 24rpx;
							line-height: 1;
							margin-top: 24rpx;
						}
					}
				}
			}
		}
		.bill_split {
			content: '';
			display: block;
			width: calc(100% - 149rpx);
			height: 20rpx;
			position: absolute;
			left: 50%;
			bottom: 62rpx;
			transform: translateX(-50%);
			background-color: #fff;
			z-index: 29;
		}
		.bg2 {
			position: absolute;
			left: 50%;
			bottom: 46rpx;
			transform: translateX(-50%);
			width: calc(100% - 102rpx);
			height: 16rpx;
		}
	}

.result {
	position: relative;
	padding: 40rpx 24rpx;
	background: #fff;
	.title {
		display: block;
		height: 45rpx;
		margin: 24rpx 0;
		font-size: 32rpx;
		font-weight: 550;
		line-height: 45rpx;
		color: #191e3e;
	}

	.result_item {
		// display: flex;
		padding: 12rpx 0;
		.text {
			font-size: 28rpx;
			line-height: 1;
			color: #191e3e;
		}
		.sign {
			display: block;
			width: 200rpx;
			height: 80rpx;
		}
		.scroll_x {
			display: flex;
			width: 100%;
			// height: 250rpx;
			margin-top: 16rpx;
			// overflow-x: scroll;
      flex-flow: row wrap;
			.scroll_x_item {
				width: 180rpx;
				height: 180rpx;
				margin-right: 15rpx;
        margin-bottom: 15rpx;
        border-radius: 10rpx;
        overflow: hidden;
        position: relative;
				&:last-child {
					margin-right: 0;
				}
				image {
					display: block;
					width: 180rpx;
					height: 180rpx;
				}
				text {
					display: block;
					font-size: 26rpx;
					line-height: 54rpx;
					color: #ffffff;
					text-align: center;
					background: #0862f6;
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          
				}
			}
		}
	}
	.inspectSeal {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 180rpx;
		height: 180rpx;
	}
	
}
	.result.inspect_result{
		// width: calc(100% - 102rpx);
    padding: 40rpx 0;
		margin: 0 auto;
	}
.view{
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		text-align: center;
		line-height: 100rpx;
		font-size: 32rpx;
		background: #fff;
		color: #195CF1;
		z-index: 90;
	}
	.view_back{
		display: block;
		height: 100rpx;
	}
	.goods {
		border-top: #191e3e 1px dashed;
		border-bottom: #191e3e 1px dashed;
		padding-bottom: 10rpx;
		.g_row {
			display: flex;
			flex-flow: row nowrap;
			padding-top: 8rpx;
			line-height: 60rpx;
			.gr_cell {
				font-size: 28rpx;
			}
			.gr_cell:first-child {
				width: 50%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.gr_cell:nth-child(2) {
				width: 25%;
				text-align: center;
			}
			.gr_cell:nth-child(3) {
				width: 25%;
				text-align: center;
			}
		}
	}
	.record {
		padding-top: 20rpx;
		border-bottom: #191e3e 1px dashed;
		.r_title {
			padding-bottom: 10rpx;
			font-size: 28rpx;
		}
		.r_list {
			display: flex;
			flex-flow: row wrap;
			// justify-content: space-around;
			align-items: flex-start;
			.r_item {
				// width: 170rpx;
				height: 200rpx;
				width: 33.33%;
				padding-bottom: 20rpx;
				padding-right: 20rpx;
				position: relative;
				image {
					width: 100%;
					height: 100%;
				}
				.img_btn {
					position: absolute;
					left: 0;
					bottom: 20rpx;
					width: calc(100% - 20rpx);
					background-color: #195cf1;
					color: #fff;
					text-align: center;
					font-size: 26rpx;
				}
			}
		}
	}
	.other {
		padding-top: 10rpx;
		position: relative;
		.p_seal {
			width: 160rpx;
			height: 160rpx;
			border-radius: 50%;
			position: absolute;
			top: 50%;
			right: 0;
			transform: translateY(calc(-50% + 10rpx));
			background: transparent;
		}
	}
  
  
  .pop_box{
    width: 600rpx;
    background-color: #fff;
    border-radius: 16rpx;
  }
  .pb_title{
    text-align: center;
    font-size: 32rpx;
    color: #333;
    padding-top: 20rpx;
  }
  .p_ctnt{
    padding: 20rpx 30rpx;
    font-size: 30rpx;
    max-height: 1000rpx;
    overflow-y: auto;
  }
  .p_btn{
    text-align: center;
    font-size: 32rpx;
    color: #3985fb;
    border-top: 1rpx solid #eee;
    height: 80rpx;
    line-height: 80rpx;
  }
</style>
