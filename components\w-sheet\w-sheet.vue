<template>
	<uni-popup 
		ref="sheet" 
		type="bottom"
		@change="change">
		<view class="sheet">
			<view class="sheet_list">
				<text class="sheet_item" v-for="item in list" :key="item.value" @click="confirmDialog(item.value)">{{ item.text }}</text>
			</view>
			<text class="sheet_cancel" @click="closeDialog">取消</text>
		</view>
	</uni-popup>
</template>

<script>
	export default{
		props: {
			visible: Boolean,
			list: {
				type: Array,
				default: () => {
					return [
						{
							text: '从相册选一张',
							value: 'album'
						}, 
						{
							text: '拍一张照片',
							value: 'camera'
						}
					]
				}
			}
		},
		watch: {
			'visible': function(newVal) {
				if (newVal) {
					this.$refs.sheet.open('bottom')
				} else {
					this.$refs.sheet.close()
				}
			}
		},
		data() {
			return {}
		},
		methods: {
			change(e) {
				if (!e.show) this.$emit('update:visible', false)
			},
			confirmDialog(val) {
				this.$emit('confirm', val)
				this.$refs.sheet.close()
			},
			closeDialog() {
				this.$emit('cancel')
				this.$refs.sheet.close()
			}
		}
	}
</script>

<style scoped lang="scss">
	.uni-popup__wrapper{
		width: 100%;
	}
	.sheet{
		width: 100%;
		height: auto;
		background: #F6F6F6;
		border-top-left-radius: 16rpx;
		border-top-right-radius: 16rpx;
		overflow: hidden;
		.sheet_list{
			width: 100%;
			height: auto;
			.sheet_item{
				background: #fff;
				display: block;
				height: 99rpx;
				border-bottom: 1rpx solid #E6E6E6;
				text-align: center;
				line-height: 99rpx;
				font-size: 30rpx;
				color: #333;
				font-weight: 400;
				&:last-child{
					border: none;
				}
			}
		}
		.sheet_cancel{
			display: block;
			height: 99rpx;
			margin-top: 20rpx;
			background: #fff;
			text-align: center;
			line-height: 99rpx;
			font-size: 30rpx;
			color: #333;
			font-weight: 400;
		}
	}
</style>