/**
 * 认证相关工具函数
 */

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export function isLoggedIn() {
  const app = getApp()
  // 检查globalData中的基本登录信息
  const hasBasicInfo = !!(app.globalData.token && app.globalData.id && app.globalData.marketId)
  
  if (!hasBasicInfo) {
    return false
  }
  
  // 进一步检查本地存储的登录信息有效性
  return isLoginValid()
}

/**
 * 检查登录信息是否有效
 * @returns {boolean} 登录信息是否有效
 */
export function isLoginValid() {
  try {
    const userInfoStr = uni.getStorageSync('userInfo')
    if (!userInfoStr) {
      return false
    }
    
    const userInfo = JSON.parse(userInfoStr)
    
    // 检查基本字段是否存在
    if (!userInfo || typeof userInfo !== 'object') {
      return false
    }
    
    if (!userInfo.token || !userInfo.id || !userInfo.marketId) {
      return false
    }
    
    // 检查时间戳是否存在和有效（30天有效期）
    if (userInfo.timestamp) {
      const maxAge = 30 * 24 * 60 * 60 * 1000 // 30天
      const isExpired = (Date.now() - userInfo.timestamp) > maxAge
      if (isExpired) {
        console.log('登录信息已过期')
        return false
      }
    }
    
    return true
  } catch (error) {
    console.error('检查登录有效性时发生错误:', error)
    return false
  }
}

/**
 * 检查登录状态，如果未登录则跳转到登录页
 * @param {boolean} showToast 是否显示提示信息
 * @returns {boolean} 是否已登录
 */
export function checkLogin(showToast = true) {
  if (!isLoggedIn()) {
    if (showToast) {
      uni.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 1500
      })
    }
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/index'
      })
    }, showToast ? 1500 : 0)
    return false
  }
  return true
}

/**
 * 检查登录状态的有效性，如果无效则自动清理并跳转
 * @param {boolean} showToast 是否显示提示信息
 * @returns {boolean} 是否已登录且有效
 */
export function checkLoginValidity(showToast = true) {
  const app = getApp()
  
  // 首先检查基本登录状态
  if (!isLoggedIn()) {
    if (showToast) {
      uni.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 1500
      })
    }
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/index'
      })
    }, showToast ? 1500 : 0)
    return false
  }
  
  // 检查登录信息的有效性
  if (!isLoginValid()) {
    console.log('登录信息无效，执行自动清理')
    
    // 清除无效的登录数据
    app.clearInvalidLoginData()
    
    if (showToast) {
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none',
        duration: 2000
      })
    }
    
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/index'
      })
    }, showToast ? 2000 : 0)
    return false
  }
  
  return true
}

/**
 * 清除登录信息
 */
export function clearLoginInfo() {
  const app = getApp()
  // 使用完全清理方法确保所有登录相关数据被清除
  app.clearAllLoginData()
  
  // 跳转到登录页面
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/login/index'
    })
  }, 100)
}

/**
 * 获取当前用户token
 * @returns {string} token
 */
export function getToken() {
  const app = getApp()
  return app.globalData.token || ''
}

/**
 * 获取当前用户ID
 * @returns {string} 用户ID
 */
export function getUserId() {
  const app = getApp()
  return app.globalData.id || ''
}

/**
 * 获取当前用户市场ID
 * @returns {string} 市场ID
 */
export function getMarketId() {
  const app = getApp()
  return app.globalData.marketId || ''
}

/**
 * 获取当前用户市场名称
 * @returns {string} 市场名称
 */
export function getMarketName() {
  const app = getApp()
  return app.globalData.marketName || ''
}

/**
 * 获取当前用户权限
 * @returns {string} 用户权限
 */
export function getUserPermission() {
  const app = getApp()
  return app.globalData.appPerm || ''
}

/**
 * 获取完整的用户信息
 * @returns {object} 用户信息对象
 */
export function getUserInfo() {
  const app = getApp()
  return {
    id: app.globalData.id || '',
    token: app.globalData.token || '',
    marketId: app.globalData.marketId || '',
    marketName: app.globalData.marketName || '',
    appPerm: app.globalData.appPerm || '',
    phone: app.globalData.phone || '',
    userName: app.globalData.userName || '',
    dutiesName: app.globalData.dutiesName || '',
    avatar: app.globalData.avatar || '',
    signature: app.globalData.signature
  }
}

/**
 * 保存用户登录信息到本地存储
 * @param {object} userInfo 用户信息对象
 */
export function saveUserInfo(userInfo) {
  try {
    const userInfoWithTimestamp = {
      ...userInfo,
      timestamp: Date.now()
    }
    
    uni.setStorageSync('userInfo', JSON.stringify(userInfoWithTimestamp))
    console.log('用户登录信息已保存')
  } catch (error) {
    console.error('保存用户登录信息失败:', error)
  }
}

/**
 * 从本地存储获取用户登录信息
 * @returns {object|null} 用户信息对象或null
 */
export function getStoredUserInfo() {
  try {
    const userInfoStr = uni.getStorageSync('userInfo')
    if (userInfoStr) {
      return JSON.parse(userInfoStr)
    }
    return null
  } catch (error) {
    console.error('获取存储的用户信息失败:', error)
    return null
  }
}