<template>
  <view class="manage-item">
    <view class="item-header">
      <view class="header-left">
        <view class="item-title">{{ carNumber }}-{{ carTypeList[item.carType] }}</view>
      </view>
      <!-- <view class="header-right"></view> -->
    </view>
    <view class="item-shop" v-if="item.shopName">所属店铺：{{ item.shopName }}</view>
    <view class="item-info">
      
      <view class="info-left">
        
        <view class="left-item">
          <image src="@/static/image/in.png" class="item-icon" />
          <view class="item-content">
            <view class="content-title">{{ item.inChannelName }}</view>
            <view class="content-time">{{ item.inTime }}</view>
          </view>
        </view>
        <view class="left-item">
          <image v-if="item.carStatus != 2" src="@/static/image/out.png" class="item-icon" />
          <image v-else src="@/static/image/out-green.png" class="item-icon" />
          <view class="item-content">
            <template v-if="item.carStatus == 2">
              <view class="content-title">{{ item.outChannelName || '-' }}</view>
              <view class="content-time">{{ item.outTime }}</view>
            </template>
            <template v-else>
              <view class="content-title">未出场</view>
            </template>
          </view>
        </view>
      </view>
      <view class="info-right">
        <view class="right-money">
          <template v-if="item.carStatus == 2">
            <template v-if="item.actualAmount">
              <text class="money-icon">￥</text>
              <text>{{ item.actualAmount }}</text>
            </template>
            <template v-else>免费</template>
          </template>
          <template v-else>
            <text class="money-icon">--</text>
          </template>
        </view>
        <view class="right-time" v-if="item.carStatus == 2">{{ item.parkingDuration }}</view>
        <view class="right-time" v-else>--</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      carTypeList: ['月租车', '临时车(商户)', '临时车'],
    }
  },
  computed: {
    carNumber() {
      return this.item.carNumber.slice(0, 2) + '·' + this.item.carNumber.slice(2)
    },
  },
  methods: {},
}
</script>

<style lang="scss" scoped>
.manage-item {
  border-radius: 16rpx;
  overflow: hidden;
  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #d4eaff;
    .header-left {
      flex: 1;
      .item-title {
        height: 80rpx;
        line-height: 80rpx;
        box-sizing: border-box;
        padding: 0 24rpx;
        color: #191e3e;
        font-weight: 550;
        font-size: 32rpx;
      }
    }
    .header-right {
      width: 260rpx;
      margin-right: 24rpx;
      text-align: right;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .item-shop {
    padding: 12rpx 24rpx;
    background: #fff;
    font-size: 30rpx;
    color: #191e3e;
    border-bottom: 1rpx solid #eee;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .item-info {
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 24rpx;

    .info-left {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 32rpx;
      .left-item {
        height: 92rpx;
        display: flex;
        .item-icon {
          width: 36rpx;
          height: 36rpx;
          margin-right: 20rpx;
          margin-top: 5rpx;
        }
        .item-content {
          .content-title {
            color: #191e3e;
            font-size: 30rpx;
            height: 45rpx;
            line-height: 45rpx;
          }
          .content-time {
            font-size: 24rpx;
            color: #7382a9;
            height: 36rpx;
            line-height: 36rpx;
            margin-top: 8rpx;
          }
        }
      }
    }
    .info-right {
      width: 200rpx;
      text-align: right;
      .right-money {
        color: #f0510a;
        font-size: 40rpx;
        .money-icon {
          font-size: 28rpx;
        }
      }
      .right-time {
        color: #191e3e;
        font-size: 28rpx;
        margin-top: 8rpx;
      }
    }
  }
}
</style>
