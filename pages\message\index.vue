<template>
  <view class="message">
    <u-navbar title="消息" placeholder :autoBack="true" height="104rpx" titleStyle="color: #191e3e; font-size: 36rpx; font-weight: 550;">
      <view class="u-nav-right" slot="right" @click="rightClick">
        <image src="@/static/image/message/clean.png" />
        <view>全部已读</view>
      </view>
    </u-navbar>
    <view class="tabs">
      <u-tabs :list="tabs" :current="currentTabIndex" :scrollable="false" @change="changeTab" lineWidth="48"></u-tabs>
    </view>
    <scroll-view scroll-y class="item-list" @scrolltolower="getMore">
      <template v-if="list.length">
        <view class="item-list-inner">
          <notice-item v-for="(item, index) in list" :item="item" :key="index" :permissions="allPermissions" :hasCarPermission="hasCarPermission"></notice-item>
          <uni-load-more :status="moreStatus" />
        </view>
      </template>
      <template v-else>
        <view class="empty-wrapper">
          <image src="@/static/image/empty.png" class="empty" />
          <text>暂无数据</text>
        </view>
      </template>
    </scroll-view>
  </view>
</template>

<script>
import NoticeItem from './components/notice-item.vue'
const app = getApp()

export default {
  components: {
    NoticeItem,
  },
  data() {
    return {
      marketId: '',
      moreStatus: 'more',
      list: [],
      filterData: {
        pageNo: 1,
        pageSize: 10,
        userId: '',
        msgIsRead: '',
      },
      tabs: [
        { name: '全部消息', value: '' },
        { name: '未读消息', value: 0 },
        { name: '已读消息', value: 1 },
      ],
      allPermissions: [],
      hasCarPermission: false,
      currentTabIndex: 0,
    }
  },
  onLoad(options) {
    this.filterData.userId = app.globalData.id
    this.marketId = app.globalData.marketId
    if (options.tab) {
      const currentTab = this.tabs.find((item) => item.value == options.tab)
      const currentIndex = this.tabs.indexOf(currentTab)
      this.currentTabIndex = currentIndex
      this.filterData.msgIsRead = currentTab.value
    }
  },
  onShow() {
    this.getPermissions()
    this.getCarPermission()
    this.search()
  },
  methods: {
    rightClick() {
      const params = {
        shiChangId: this.marketId,
        userId: this.filterData.userId,
      }
      this.$api.readAllMessage(params).then(() => {
        this.search()
      })
    },
    search() {
      this.list = []
      this.moreStatus = 'more'
      this.filterData.pageNo = 1
      this.getList()
    },
    getList() {
      if (this.moreStatus === 'noMore') return
      this.moreStatus = 'loading'
      this.$api.getMessageList(this.filterData).then((res) => {
        this.moreStatus = 'more'
        if (res.data.records.length < this.filterData.pageSize) {
          this.moreStatus = 'noMore'
        }
        this.list = [...this.list, ...res.data.records]
      })
    },
    changeTab(item) {
      this.currentTabIndex = item.index
      this.filterData.msgIsRead = item.value
      this.search()
    },
    getMore() {
      ++this.filterData.pageNo
      this.getList()
    },
    getPermissions() {
      this.$api.getMarketPermission({ marketId: this.marketId }).then((res) => {
        this.allPermissions = res.data
      })
    },
    getCarPermission() {
      const params = { userId: app.globalData.id }
      this.$api.hasCarPermisson(params).then((res) => {
        this.hasCarPermission = res.data
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.message {
  width: 100%;
  height: 100vh;
  background: #edf0f3;
  color: #191e3e;
  .u-nav-right {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 4rpx;
    }
  }
  .tabs {
    background: #fff;
    padding-bottom: 10rpx;
    border-bottom: 1rpx solid #edf0f3;
    box-sizing: border-box;
  }
  .item-list {
    height: calc(100vh - var(--status-bar-height) - 104rpx - 100rpx);
    box-sizing: border-box;
    .item-list-inner {
      padding: 24rpx;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      gap: 18rpx;
    }
    .empty-wrapper {
      width: 100%;
      height: 60%;
      display: flex;
      justify-content: center;
      align-items: center;
      display: flex;
      flex-direction: column;
      .empty {
        width: 250rpx;
        height: 150rpx;
      }
      text {
        font-family: Source Han Sans, Source Han Sans;
        font-size: 28rpx;
        color: #7382a9;
      }
    }
  }
}
</style>
