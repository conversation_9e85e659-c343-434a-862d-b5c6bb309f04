<template>
	<view class="tab_block">
		<text class="tab_line" :style="{ 'padding-top': statusBarHeight + 'px' }"></text>
		<view class="tab" :style="{ 'padding-top': statusBarHeight + 'px', background: tabBackground }">
			<uni-icons type="back" size="27px" :color="titleColor" class="tab_icon" @click="back"></uni-icons>
			<text class="tab_title" :style="{color: titleColor}">{{ title }}</text>
			<text class="tab_add" @click="rightClick" v-if="showRight" :style="{color: rightColor}">{{ rightText }}</text>
		</view>
	</view>
</template>

<script>
	const app = getApp()
	export default {
		props: {
			title: {
				type: String,
				default: '标题'
			},
			titleColor: {
				type: String,
				default: '#000'
			},
			tabBackground: {
				type: String,
				default: '#fff'
			},
			showRight: {
				type: Boolean,
				default: false
			},
			rightColor: {
				type: String,
				default: '#333'
			},
			rightText: {
				type: String,
				default: '更多'
			}
		},
		data() {
			return {
				statusBarHeight: app.globalData.statusBarHeight
			}
		},
		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			rightClick() {
				this.$emit('right-click')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tab_block{
		width: 100%;
		view,text{
			line-height: 1;
		}
		.tab_line{
			display: block;
			height: 70rpx;
		}
		.tab{
			width: 100%;
			height: 70rpx;
			display: flex;
			align-items: center;
			position: fixed;
			left: 0;
			top: 0;
			z-index: 5;
			transition: all .3s;
			.tab_icon{
				position: absolute;
				left: 10px;
				transition: all .3s;
			}
			.tab_title{
				flex: 1;
				display: block;
				text-align: center;
				font-size: 34rpx;
				// font-weight: bold;
				transition: all .3s;
			}
			.tab_add{
				position: absolute;
				right: 24rpx;
				font-size: 34rpx;
			}
		}
	}
</style>