import url from './baseUrl.js'

const baseUrl = url.baseUrl

export const request = (method, url, data = {}, toast = true, hiddenMsg = true) => {
  const app = getApp()
  if (toast) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    })
  }
  return new Promise((resolve, reject) => {
    let header = {}
    if (app.globalData.token) {
      header.Authorization = `Bearer ${app.globalData.token}`
    }
    uni.request({
      method,
      url: baseUrl + url, //仅为示例，并非真实接口地址。
      header,
      data,
      success: (res) => {
        if (toast) {
          uni.hideLoading()
        }
        if (res.statusCode == 500) {
          uni.showToast({
            title: res.data.error,
            icon: 'none',
            duration: 1500,
            mask: true
          })
          setTimeout(() => {
            reject()
          }, 1500)
          return
        }
        if (res.data.code) {
          if (res.data.code == 200) {
            if (res.data.msg) {
              if (hiddenMsg) {
                uni.showToast({
                  title: res.data.msg,
                  icon: 'none',
                  duration: 1500,
                  mask: true
                })
                setTimeout(() => {
                  resolve(res.data)
                }, 1600)
              } else {
                resolve(res.data)
              }
            } else {
              resolve(res.data)
            }
            return
          } else if (res.data.code == 500) {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 1500,
              mask: true
            })
            reject()
          } else if (res.data.code == 401) {
            // token失效，使用自动清理机制
            console.log('Token已失效，执行自动清理机制');
            app.handleTokenInvalidation()
          }

        } else {
          resolve(res.data)
        }
      },
      fail: err => {
        if (toast) {
          uni.hideLoading()
        }
        uni.showToast({
          title: err,
          icon: 'none',
          duration: 2000
        })
        reject()
      }
    })
  })
}

export const uploadFile = (url, filePath, name = 'file', formData) => {
  const app = getApp()
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  return new Promise((resolve, reject) => {
    let header = {}
    if (app.globalData.token) {
      header.Authorization = `Bearer ${app.globalData.token}`
    }
    uni.uploadFile({
      url: baseUrl + url, //仅为示例，并非真实接口地址。
      filePath,
      name,
      formData,
      header,
      success: (res) => {
        uni.hideLoading()
        if (res.statusCode == 200) {
          let data = JSON.parse(res.data)
          if (data.code == 200) {
            resolve(data)
            return
          }
          uni.showToast({
            title: data.msg,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail: err => {
        uni.hideLoading()
        uni.showToast({
          title: err,
          icon: 'none',
          duration: 2000
        })
      }
    })
  })
}

export const uploadFiles = (url, files, formData) => {
  const app = getApp()
  uni.showLoading({
    title: '加载中...',
    mask: true
  })
  return new Promise((resolve, reject) => {
    let header = {}
    if (app.globalData.token) {
      header.Authorization = `Bearer ${app.globalData.token}`
    }
    uni.uploadFile({
      url: baseUrl + url, //仅为示例，并非真实接口地址。
      files,
      filePath: '',
      formData,
      header,
      success: (res) => {
        uni.hideLoading()
        if (res.statusCode == 200) {
          let data = JSON.parse(res.data)
          if (data.code == 200) {
            resolve(data)
            return
          }
          uni.showToast({
            title: data.msg,
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail: err => {
        uni.hideLoading()
        uni.showToast({
          title: err,
          icon: 'none',
          duration: 2000
        })
      }
    })
  })
}