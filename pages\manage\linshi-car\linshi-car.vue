<template>
  <view class="container">
    <uni-nav-bar fixed statusBar :border="false" height="100rpx" right-width="140rpx" @clickLeft="toBack">
      <view class="navbar-title">
        <view class="active">临时车辆</view>
        <view @click="toLong">月租车辆</view>
      </view>
      <template #left>
        <uni-icons type="left" color="#7382A9" size="20" />
      </template>
    </uni-nav-bar>
    <view class="search-bar">
      <view class="search-wrapper">
        <view class="wrapper-left">
          <image src="@/static/image/price/search.png" style="width: 40rpx; height: 40rpx" />
          <input class="search-input" v-model="filterData.carNumber" placeholder="请输入店铺名称或车牌号搜索" placeholder-style="color: #AEB5C8; font-size: 24rpx;" />
          <uni-icons v-if="filterData.carNumber" type="clear" size="20" @click="clear" />
        </view>

        <view class="search-btn" @click="search">搜索</view>
      </view>
    </view>
    <scroll-view scroll-y class="scroll-wrapper" @scrolltolower="getMore">
      <view class="result">
        <view class="result-inner">
          <view class="inner-left">
            <view class="left-title">{{ count.total }}</view>
            <view class="left-subtitle">当前检索</view>
          </view>
          <view class="inner-right">
            <view class="right-item item2" :class="{ active: filterData.status == '0' }" @click="handleFilter('0')">
              <image src="@/static/image/car-4.png" class="car-icon" />
              <view class="right-title">{{ count.count1 }}</view>
              <view class="right-subtitle">申请中</view>
            </view>
            <view class="right-item" :class="{ active: filterData.status == '1' }" @click="handleFilter('1')">
              <image src="@/static/image/car-1.png" class="car-icon" />
              <view class="right-title">{{ count.count2 }}</view>
              <view class="right-subtitle">已通过</view>
            </view>
            <view class="right-item item3" :class="{ active: filterData.status == '2' }" @click="handleFilter('2')">
              <image src="@/static/image/car-5.png" class="car-icon" />
              <view class="right-title">{{ count.count3 }}</view>
              <view class="right-subtitle">已拒绝</view>
            </view>
          </view>
        </view>
      </view>
      <template v-if="list.length">
        <view class="scroll-wrapper-inner">
          <car-item v-for="(item, index) in list" :item="item" :freeTime="freeTime" :key="index" @resolve="handleResolve" @reject="handleReject"></car-item>
          <uni-load-more :status="moreStatus" />
        </view>
      </template>
      <template v-else>
        <view class="empty-wrapper">
          <image src="@/static/image/empty.png" class="empty" />
          <text>暂无数据</text>
        </view>
      </template>
    </scroll-view>
    <u-modal :show="showModal" title="注意" :duration="100" showCancelButton :content="`申请通过后，车辆在${freeTime}分钟内离场免费！`" @cancel="showModal = false" @confirm="handleConfirm"></u-modal>
    <u-modal :show="showModal2" title="注意" :duration="100" showCancelButton content="确认要拒绝吗？" @cancel="showModal2 = false" @confirm="handleConfirm2"></u-modal>
  </view>
</template>

<script>
import CarItem from './components/car-item.vue'
export default {
  components: {
    CarItem,
  },
  data() {
    return {
      app: null,
      filterData: {
        pageNum: 1,
        pageSize: 10,
        marketId: '',
        carNumber: '',
        status: '',
      },
      list: [],
      moreStatus: 'more',
      freeTime: 0,
      showModal: false,
      showModal2: false,
      currentItem: {},
      count: {
        total: 0,
        count1: 0,
        count2: 0,
        count3: 0,
      },
    }
  },
  onLoad(e) {
    const status = e.status
    if (status) {
      this.filterData.status = status
    }
    this.app = getApp()
    this.filterData.marketId = this.app.globalData.marketId
    this.getRule()
  },
  onShow() {
    this.search()
  },
  methods: {
    toBack() {
      uni.navigateBack()
    },
    getRule() {
      this.$api.getFreeRule({ marketId: this.filterData.marketId }).then((res) => {
        this.freeTime = res.data.shopTemporaryCarTime * 60
      })
    },
    search() {
      this.list = []
      this.moreStatus = 'more'
      this.filterData.pageNum = 1
      this.getList()
    },
    getList() {
      if (this.moreStatus === 'noMore') return
      this.moreStatus = 'loading'
      this.$api.carTemporaryList(this.filterData).then((res) => {
        this.moreStatus = 'more'
        if (res.data.list.records.length < this.filterData.pageSize) {
          this.moreStatus = 'noMore'
        }
        this.list = [...this.list, ...res.data.list.records]
        this.count = {
          total: res.data.list.total,
          count1: res.data.count1,
          count2: res.data.count2,
          count3: res.data.count3,
        }
      })
    },
    handleFilter(value) {
      this.filterData.status = this.filterData.status == value ? '' : value
      this.search()
    },
    getMore() {
      ++this.filterData.pageNum
      this.getList()
    },
    clear() {
      this.list = []
      this.moreStatus = 'more'
      this.filterData.pageNum = 1
      this.filterData.carNumber = ''
      this.getList()
    },
    handleResolve(item) {
      this.showModal = true
      this.currentItem = item
    },
    handleConfirm() {
      const params = {
        id: this.currentItem.id,
        status: 1,
        userId: this.app.globalData.id,
      }
      this.$api.auditCarTemporary(params).then((res) => {
        this.showModal = false
        this.search()
      })
    },
    handleReject(item) {
      this.showModal2 = true
      this.currentItem = item
    },
    handleConfirm2() {
      const params = {
        id: this.currentItem.id,
        status: 2,
        userId: this.app.globalData.id,
      }
      this.$api.auditCarTemporary(params).then((res) => {
        this.showModal2 = false
        this.search()
      })
    },
    toLong() {
      uni.redirectTo({
        url: '/pages/manage/linshi-car/long-car',
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  height: 100vh;
  background: #edf0f3;
  .navbar-title {
    flex: 1;
    color: #191e3e;
    line-height: 100rpx;
    font-size: 34rpx;
    display: flex;
    justify-content: center;
    gap: 80rpx;
    align-items: center;
    view {
      position: relative;
      &.active {
        color: #191e3e;
        font-size: 36rpx;
        font-weight: 550;
        &::after {
          content: '';
          width: 80%;
          height: 4rpx;
          background-color: #0862f6;
          border-radius: 4rpx;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }
  .search-bar {
    background-color: #fff;
    padding: 15rpx 24rpx;
    .search-wrapper {
      height: 80rpx;
      background: #f1f3f8;
      border-radius: 16rpx;
      border: 1rpx solid #d8d8d8;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-left: 24rpx;
      gap: 10rpx;
      .wrapper-left {
        flex: 1;
        display: flex;
        align-items: center;
        .search-input {
          flex: 1;
          margin-left: 24rpx;
        }
      }
      .search-btn {
        width: 120rpx;
        height: 60rpx;
        line-height: 60rpx;
        color: #fff;
        font-size: 28rpx;
        text-align: center;
        background: #0862f6;
        border-radius: 8rpx;
        margin-right: 10rpx;
      }
    }
  }
  .scroll-wrapper {
    height: calc(100vh - 100rpx - 112rpx - var(--status-bar-height));
    box-sizing: border-box;
    .empty-wrapper {
      width: 100%;
      height: 60%;
      display: flex;
      justify-content: center;
      align-items: center;
      display: flex;
      flex-direction: column;
      .empty {
        width: 250rpx;
        height: 150rpx;
      }
      text {
        font-family: Source Han Sans, Source Han Sans;
        font-size: 28rpx;
        color: #7382a9;
      }
    }
    .scroll-wrapper-inner {
      padding: 16rpx 24rpx;
      display: flex;
      flex-direction: column;
      gap: 18rpx;
    }
  }
  .result {
    padding: 16rpx 24rpx 0;
    .result-inner {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(360deg, #195cf1 0%, #379dff 100%);
      border-radius: 16rpx;
      height: 190rpx;
      box-sizing: border-box;
      padding: 8rpx;
      .inner-left {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 24rpx;
        .left-title {
          font-size: 48rpx;
        }
        .left-subtitle {
          opacity: 0.8;
          margin-top: 18rpx;
          letter-spacing: 1rpx;
        }
      }
      .inner-right {
        width: 500rpx;
        height: 100%;
        background: linear-gradient(180deg, #f1f8ff 0%, #ffffff 100%);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 34rpx 24rpx 16rpx;
        .right-item {
          width: 140rpx;
          height: 100%;
          border-radius: 16rpx;
          background: linear-gradient(180deg, #9ef3a6 0%, #fefefe 100%);
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          &.active {
            border-bottom: 1px solid #9ef3a6;
            box-shadow: 2px 2px 7px 0 #9ef3a6;
            .right-title {
              color: #0b981e;
              font-weight: 550;
            }
          }
          &.item2 {
            background: linear-gradient(180deg, #c1daff 0%, #fefefe 100%);
            &.active {
              border-bottom: 1px solid #c1daff;
              box-shadow: 2px 2px 7px 0 #c1daff;
              .right-title {
                color: #0a52ca;
                font-weight: 550;
              }
            }
          }
          &.item3 {
            background: linear-gradient(180deg, #ffd6c4 0%, #fefefe 100%);
            &.active {
              border-bottom: 1px solid #ffd6c4;
              box-shadow: 2px 2px 7px 0 #ffd6c4;
              .right-title {
                color: #f0510a;
                font-weight: 550;
              }
            }
          }
          .car-icon {
            position: absolute;
            width: 55rpx;
            height: 55rpx;
            margin-top: -22rpx;
          }
          .right-title {
            margin-top: 44rpx;
            color: #191e3e;
            font-size: 34rpx;
            height: 34rpx;
            line-height: 34rpx;
          }
          .right-subtitle {
            margin-top: 12rpx;
            color: #7382a9;
            font-size: 22rpx;
            height: 24rpx;
            line-height: 24rpx;
          }
        }
      }
    }
  }
}
</style>
