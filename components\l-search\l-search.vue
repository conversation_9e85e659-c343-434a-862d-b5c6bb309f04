<template>
  <view class="list-search">
    <view v-if="isShowBack" class="back" @click="back">
      <uni-icons type="left" color="#7382A9" size="24"></uni-icons>
    </view>
    <view class="input-wrapper">
      <view class="left">
        <image src="@/static/image/search.png" />
        <input :value="value" @input="onInput" :placeholder="placeholder" placeholder-style="color: #9DA8C7; font-size: 30rpx" />
        <uni-icons v-if="value" type="close" color="#7382A9" size="20" @click="clear" />
      </view>
      <view class="right" @click="search">搜索</view>
    </view>
  </view>
</template>

<script>
  export default {
    name:"l-search",
    props: {
      placeholder: {
        type: String,
        default: '请输入'
      },
      isShowBack: {
        type: Boolean,
        default: false
      },
      value: {
        type: String,
        default: ''
      }
    },
    methods: {
      back() {
        uni.navigateBack()
      },
      search() {
        this.$emit('search', this.value)
      },
      clear() {
        this.$emit('input', '')
        this.$emit('clear')
      },
      onInput(e) {
        this.$emit('input', e.target.value)
      }
    }
  }
</script>

<style lang="scss" scoped>
.list-search {
  width: 100%;
  height: 100rpx;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 24rpx 10rpx 24rpx;
  box-sizing: border-box;
  gap: 20rpx;
  border-radius: 16rpx;
  
  .back {
    width: 52rpx;
    height: 52rpx;
    display: flex;
    align-items: center;
  }
  .input-wrapper {
    flex: 1;
    height: 80rpx;
    background: #EDF0F3;
    border-radius: 8rpx;
    border: 1rpx solid #DDDDDD;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20rpx;
    padding: 0 10rpx 0 16rpx;
    box-sizing: border-box;
    .left {
      flex: 1;
      display: flex;
      align-items: center;
      image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 16rpx;
      }
      input {
        flex: 1;
      }
    }
    .right {
      width: 104rpx;
      height: 60rpx;
      line-height: 60rpx;
      background: #0862F6;
      border-radius: 4rpx;
      text-align: center;
      font-size: 28rpx;
      color: #FFFFFF;
    }
  }
}
</style>