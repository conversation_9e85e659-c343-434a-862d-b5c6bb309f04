<template>
    <view class="pop_ctnt">
      <view class="title" v-if="opts.type==='1'">{{opts.state==='1'?'处理结果':'经营巡查异常'}}</view>
      <view class="title" v-else-if="opts.type==='2'">{{opts.state==='1'?'处理结果':'设备巡查异常'}}</view>
      <view class="title" v-else-if="opts.type==='3'">{{opts.state==='1'?'处理结果':'入场查验异常'}}</view>
      <view class="title" v-else-if="opts.type==='4'">{{opts.state==='1'?'处理结果':'检测报告异常'}}</view>
      
      <view class="form">
        <view class="form_row" v-if="opts.state==='1'">
          <view class="fr_label"><text>预警类型：</text></view>
          <view class="fr_val" v-if="opts.type==='1'">经营巡查异常</view>
          <view class="fr_val" v-else-if="opts.type==='2'">设备巡查异常</view>
          <view class="fr_val" v-else-if="opts.type==='3'">入场查验异常</view>
          <view class="fr_val" v-else-if="opts.type==='4'">检测报告异常</view>
        </view>
        
        <view :class="['form_row situation', opts.state==='0'?'no':'yes']">
          <view class="fr_label">
            <text v-if="opts.state==='0'" style="color:#dd524d;font-weight:bold;">*</text>
            <text>处理情况：</text>
          </view>
          <textarea class="fr_val_textarea" v-if="opts.state==='0'" v-model="processSituation" placeholder="请输入预警处理情况（最多500字）" placeholder-style="color:#9DA8C7;font-size:26rpx;" maxlength="500"></textarea>
          <view class="fr_val_situation" v-else-if="opts.state==='1'">{{opts.processSituation}}</view>
        </view>
        
        <view class="form_row upload">
          <view v-if="opts.state==='0'" class="fr_label"><text>处理照片：</text><text class="tip">实际处理照片，1-9张</text></view>
          <view v-else-if="opts.state==='1' && fileList.length>0" class="fr_label"><text>处理照片：</text></view>
          <!-- 只有 入场查验类型type==='3' 且 预警未处理state==='0' 才生效 -->
          <radio-group v-if="shopId && opts.type==='3' && opts.state==='0'" class="radio_group" @change="uploadTypeChange">
            <label class="radio_item"><radio value="1" :checked="uploadType==='1'" />本地上传照片</label>
            <label class="radio_item"><radio value="2" :checked="uploadType==='2'" />选择店铺检测报告</label>
          </radio-group>
          
          <view class="file_list">
            <template v-if="fileList.length>0">
              <template v-if="opts.state==='0'">
                <view class="fl_item" v-for="(item, idx) in fileList" :key="idx">
                  <image class="fli_img" :src="item.url" @click="preview(idx)"></image>
                  <view class="fli_cover" @click="handleRemove(idx)">删除</view>
                </view>
              </template>
              <template v-else-if="opts.state==='1'">
                <view class="fl_item" v-for="(item, idx) in fileList" :key="idx"  @click="preview(idx)">
                  <image class="fli_img" :src="item.url"></image>
                  <view class="fli_cover">查看原图</view>
                </view>
              </template>
              
            </template>
            <view class="fl_item btn_upload" v-if="fileList.length<9 && opts.state==='0'" @click="beforeChooseImg">
              <view class="up_bg"></view>
              <uni-icons v-if="uploadType==='1'" type="camera-filled" class="ico_camera" color="#191E3E" size="40"></uni-icons>
              <view v-else class="ico_camera text">点击选择店铺检测报告</view>
            </view>
          </view>
        </view>
        
        <template  v-if="opts.state==='1'">
          <view class="form_row manager">
            <view class="fr_label">处理领导：</view>
            <view class="fr_val" v-if="opts.signature"><img :src="opts.signature" class="sign_img" alt="" /></view>
            <view class="fr_val" v-else>{{opts.managerName}}</view>
          </view>
          <view class="form_row">
            <view class="fr_label">处理时间：</view>
            <view class="fr_val" >{{opts.processTime}}</view>
          </view>
        </template>
        
      </view>
      
      <view class="btns" v-if="opts.state==='0'">
        <view class="btn_cancel" @click="cancel">暂不处理</view>
        <view class="btn_confirm" @click="confirm">提交</view>
      </view>

    </view>
</template>

<script>
  const app = getApp()
  export default {
    props: {
      shopId: {
        type: String,
        default: '',
      },
      shopName: {
        type: String,
        default: '',
      },
      warnImgs: {
        type: Array,
        default: () => {
          return []
        }
      },
      opts: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    data () {
      return {
        global: app.globalData,
        processSituation: '',
        processPhoto: '',
        fileList: [],
        count: 9, // 剩余可上传的图片数量  最多9张
        uploadType: '1', // 1本地 2检测报告
      }
    },
    watch: {
      warnImgs: {
        handler (newVal, oldVal) {
          this.fileList = this.fileList.concat(newVal.map(item => { return {url: item} }))
          this.count = 9 - this.fileList.length
        }
      },
      count: {
        handler (newVal) {
          this.$emit('countChange', newVal)
        }
      }
    },
    mounted () {
      this.updateValue(this.opts) 
    },
    methods: {
      updateValue (opts) {
        if (opts.state==='1') { // 1已处理 => 数据回显
          this.processSituation = this.opts.processSituation
          this.processPhoto = ''
          if (opts.processPhoto) {
            this.fileList = (opts.processPhoto.split(',')).map(item => { return {url: item} })
          }
        }
      },
      
      uploadTypeChange (e) {
        const val = e.detail.value
        this.uploadType = val
      },
      
      // 选择照片
      beforeChooseImg () {
        if (this.uploadType==='1') { // 拍照或本地上传
          this.localUpload()
        } else if (this.uploadType==='2') { // 检测报告
          uni.navigateTo({url: `/pages/check/records-detail/shop-report-list?shopId=${this.shopId}&shopName=${this.shopName}&count=${this.count}`})
        }
      },
      // 本地上传 - 前置权限判断
      localUpload () {
        // #ifdef H5
        this.chooseImg()
        // #endif
        
        // #ifdef APP-PLUS
        const cam = app.checkPermission('android.permission.CAMERA')
        const sto = app.checkPermission('android.permission.READ_EXTERNAL_STORAGE')
        if (!cam) { // 未授权
          uni.showModal({
            title: '提示',
            content: '请开启摄像头权限用于拍照上传预警信息处理照片',
            showCancel: false,
            confirmText: '知道了',
            success: () => {
              this.chooseImg()
            }
          });
        } else if (!sto) {
          uni.showModal({
            title: '提示',
            content: '请开启相册权限用于读取相册图片上传预警信息处理照片',
            showCancel: false,
            confirmText: '知道了',
            success: () => {
              this.chooseImg()
            }
          });
        } else {
          this.chooseImg()
        }
        // #endif
      },
      // 选择照片 - 上传
      chooseImg () {
        uni.chooseImage({
          count: this.count,
          sourceType: ['camera', 'album'],
          success: (res) => {
            res.tempFiles.map(item => {
              this.$api.uploadFile(item.path, {type: 'market'}).then(res1 => {
                this.count = this.count - 1 // 更新文件数量 最多9张
              	this.fileList.push({url: res1.msg})
              })
            })
          }
        })
      },
      
      // 重新上传（删除）
      handleRemove (idx) {
        this.fileList.splice(idx, 1)
        this.count = this.count+1
      },
      preview (idx) {
        const imgs = this.fileList.map(item => { return item.url })
        uni.previewImage({current: idx, urls: imgs, indicator: 'number'})
      },
      // 取消
      cancel () {
        // this.$refs.popup.close()
        uni.navigateBack()
      },
      // 提交
      confirm () {
        if (!this.processSituation) {
          uni.showToast({title: '请输入内容', icon: 'error'})
          return false
        }
        
        let processPhoto = ''
        if (this.fileList.length>0) {
          const tmp = this.fileList.map(item => { return item.url })
          processPhoto = tmp.join(',')
        }
        
        const params = {
          userId: this.global.id,
          warnId: this.opts.id,
          processSituation: this.processSituation,
          processPhoto: processPhoto
        }
        
        this.$api.processWarn(params).then(() => {
          uni.removeStorageSync('warnImgs')
          uni.setStorageSync('reload', true)
          uni.navigateBack()
        })
        
      }
    }
  }
</script>

<style scoped>
  view, textarea{
    box-sizing: border-box;
  }
  .pop_ctnt{
    /* padding: 30rpx; */
    margin-top: 20rpx;
    width: 100%;
  }
  .title{
    font-size: 15px;
    font-weight: bold;
    padding: 10px 0;
    border-top: #191E3E  1rpx dashed;
  }
  .desc{
    margin-top: 30rpx;
    color: #7382A9;
    padding-bottom: 10rpx;
    text-align: center;
  }
  
  .form{
    background-color: #fff;
    border-radius: 16rpx;
    width: 100%;
    /* padding: 20rpx 0; */
  }
  .form_row, .form_row.situation.yes{
    margin-bottom: 10rpx;
    display: flex;
    flex-flow: row nowrap;
  }
 .form_row.situation.no{
    display: block;
  }
  .form_row.manager{
    margin-top: 10rpx;
  }

  .fr_label, .form_row.situation.yes .fr_label{
    /* font-weight: bold; */
    width: 150rpx;
    color: #191E3E;
    font-size: 26rpx;
    padding-bottom: 10rpx;
    line-height: normal;
  }
  
  .fr_val, .form_row.situation.yes .fr_val_situation{
    flex: 1;
    position: relative;
    color: #191E3E;
    word-break: break-all;
    font-size: 26rpx;
    line-height: normal;
  }
  .fr_val_textarea{
    border: #ddd 1rpx solid;
    border-radius: 16rpx;
    width: 100%;
    height: 150rpx;
    padding: 10rpx;
    font-size: 26rpx;
  }
  .fr_val_situation{
    word-break: break-all;
    line-height: normal;
  }
  .sign_img{
    width: 240rpx;
    height: 100rpx;
  }
  .upload .tip{
    font-size: 24rpx;
    color: #7382A9;
    padding-left: 20rpx;
    font-weight: normal;
  }
  .form_row.upload{
    margin-bottom: 0;
    display: block;
  }
  .form_row.situation .fr_label,
  .form_row.upload .fr_label{
    width: 100%;
  }
  .comp_upload{
    display: flex;
    flex-flow: row nowrap;
  }
  .btn_upload{
    width: 200rpx;
    height: 200rpx;
    position: relative;
  }
  .up_bg{
    width: 100%;
    height: 100%;
    background: #EDF0F3;
    border-radius: 8rpx;
  }
  .ico_camera{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .ico_camera.text{
    line-height: 1.2;
    font-size: 24rpx;
    color: #7382a9;
    width: 80%;
    text-align: center;
  }
  .file_list{
    display: flex;
    flex-flow: row wrap;
    align-items: flex-start;
    gap: 20rpx;
    padding-bottom: 10rpx;
  }
  .fl_item{
    width: 160rpx;
    height: 160rpx;
    /* padding-right: 10rpx; */
    /* padding-bottom: 10rpx; */
    position: relative;
    /* margin-bottom: 10rpx; */
  }
  .fli_img{
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
    overflow: hidden;
  }
  .fli_cover{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #0862F6;
    color: #fff;
    border-bottom-left-radius: 8rpx;
    border-bottom-right-radius: 8rpx;
    text-align: center;
    font-size: 26rpx;
    height: 50rpx;
    line-height: 50rpx;
  }
  .radio_group{
    padding-bottom: 20rpx;
  }
  .radio_item{
    font-size: 26rpx;
  }
  .radio_item:nth-child(1) {
    margin-right: 20rpx;
  }
  .radio_item/deep/.uni-radio-input{
    width: 26rpx;
    height: 26rpx;
  }
  
  
  .btns{
    margin-top: 20rpx;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
  }
  .btn_cancel, .btn_confirm{
    width: 48%;
    text-align: center;
    border-radius: 10rpx;
    color: #191E3E;
    font-size: 28rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
  }
  .btn_cancel{
    border: #585D77 1rpx solid;
  }
  .btn_confirm{
    background: linear-gradient( 360deg, #195CF1 0%, #379DFF 100%);
    color: #fff;
  }
  .tip{
    padding: 30rpx 0;
    font-size: 24rpx;
    color: #7382A9;
    word-break: break-all;
  }
  .btn_ok{
    background: linear-gradient( 360deg, #195CF1 0%, #379DFF 100%);
    color: #fff;
    text-align: center;
    border-radius: 10rpx;
    font-size: 28rpx;
    height: 80rpx;
    line-height: 80rpx;
    margin-top: 40rpx;
  }
</style>