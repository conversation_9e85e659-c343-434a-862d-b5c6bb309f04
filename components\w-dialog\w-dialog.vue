<template>
	<uni-popup 
		ref="dialog" 
		type="center"
		@change="change">
		<view class="dialogue">
			<text class="dialogue_title">{{ title }}</text>
			<text class="dialogue_text">{{ text }}</text>
			<view class="dialogue_btn">
				<template v-if="showCancel">
					<text class="dialogue_cancel" @click="closeDialog">{{ cancelText }}</text>
					<text class="dialogue_line"></text>
					<text class="dialogue_confirm" :style="{color: confirmColor}" @click="confirmDialog">{{ confirmText }}</text>
				</template>
				<template v-else>
					<text class="dialogue_confirm" :style="{color: confirmColor}" @click="confirmDialog">{{ confirmText }}</text>
				</template>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	export default{
		props: {
			visible: Boolean,
			title: {
				type: String,
				default: '提示'
			},
			text: String,
			confirmText: {
				type: String,
				default: '确认'
			},
			confirmColor: {
				type: String,
				default: '#333'
			},
			cancelText: {
				type: String,
				default: '取消'
			},
			showCancel: {
				type: Boolean,
				default: true
			}
		},
		watch: {
			'visible': function(newVal) {
				if (newVal) {
					this.$refs.dialog.open('center')
				} else {
					this.$refs.dialog.close()
				}
			}
		},
		data() {
			return {
				
			}
		},
		methods: {
			change(e) {
				if (!e.show) this.$emit('update:visible', false)
			},
			confirmDialog() {
				this.$emit('confirm')
				this.$refs.dialog.close()
			},
			closeDialog() {
				this.$emit('cancel')
				this.$refs.dialog.close()
			}
		}
	}
</script>

<style scoped lang="scss">
	.dialogue{
		width: 600rpx;
		height: auto;
		padding-top: 35rpx;
		background: #fff;
		border-radius: 16rpx;
		.dialogue_title{
			display: block;
			font-size: 34rpx;
			font-weight: 500;
			text-align: center;
			color: #111;
			padding-bottom: 30rpx;
		}
		.dialogue_text{
			display: block;
			width: 85%;
			margin: 0 7.5%;
			font-size: 30rpx;
			color: #666;
			text-align: center;
			line-height: 40rpx;
			padding-bottom: 40rpx;
		}
		.dialogue_btn{
			height: 95rpx;
			border-top: 1rpx solid #f1f1f1;
			display: flex;
			text{
				display: block;
			}
			.dialogue_confirm{
				flex: 1;
				font-size: 34rpx;
				color: #333;
				font-weight: 400;
				display: block;
				text-align: center;
				line-height: 95rpx;
			}
			.dialogue_line{
				width: 1rpx;
				height: 100%;
				background: #f1f1f1;
			}
			.dialogue_cancel{
				flex: 1;
				font-size: 34rpx;
				color: #999;
				font-weight: 400;
				display: block;
				text-align: center;
				line-height: 95rpx;
			}
		}
	}
</style>