<template>
    <view class="w_radio_block">
        <view class="w_radio" @click="checkRadio(yesText)">
            <image :src="active == yesText? require('@/static/image/radio_check.png'): require('@/static/image/radio_default.png')"></image>
            <text>{{ yesText }}</text>
        </view>
        <view class="w_radio" @click="checkRadio(noText)">
            <image :src="active == noText? require('@/static/image/radio_check.png'): require('@/static/image/radio_default.png')"></image>
            <text>{{ noText }}</text>
        </view>
    </view>
</template>
<script>
export default {
    props: {
        yesText: {
            type: String,
            default: '是'
        },
        noText: {
            type: String,
            default: '否'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        value: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            active: this.value
        }
    },
    methods: {
        checkRadio(value) {
            if (this.disabled) return
            this.active = value
            this.$emit('change', value)
        },
        reset () {
          this.active = ''
        }
    }
}
</script>
<style scoped>
.w_radio_block{
    display: flex;
}
.w_radio{
  display: flex;
  align-items: center;
  padding: 10rpx 10rpx;
  margin: 0 10rpx;
}
.w_radio image{
  display: block;
  width: 28rpx;
  height: 28rpx;
}
.w_radio text{
  font-size: 32rpx;
  color: #333;
  margin-left: 16rpx;
  white-space: nowrap;
}
</style>
