
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/login/index","pages/home/<USER>/index","pages/home/<USER>/index","pages/home/<USER>/index","pages/home/<USER>/index","pages/home/<USER>/index","pages/home/<USER>/index","pages/home/<USER>/index","pages/home/<USER>/index","pages/home/<USER>/index","pages/inspect/index/index","pages/inspect/inspect-progress/index","pages/inspect/record/index","pages/pending/index/index","pages/manage/index/index","pages/manage/jingying/index","pages/manage/kaoqin/index","pages/manage/kaoqin/leave","pages/manage/week/index","pages/manage/week/record","pages/manage/week/detail","pages/manage/month/index","pages/manage/month/record","pages/manage/month/detail","pages/manage/year/index","pages/manage/year/record","pages/manage/year/detail","pages/manage/web-view/index","pages/manage/today-price/index","pages/manage/today-price/choose-products","pages/mine/index/index","pages/mine/user/index","pages/mine/sign/index","pages/mine/about/index","pages/mine/agreement/index","pages/check/index/index","pages/check/manual-check/index","pages/check/check-detail/index","pages/check/check-result/index","pages/check/records-detail/index","pages/check/records-detail/shop-report-list","pages/price/index/index","pages/price/history/history","pages/testing/index/index","pages/testing/history/history","pages/berth/index/index","pages/staff/index/index","pages/staff/records/records","pages/manage/shop/index","pages/home/<USER>/list","pages/home/<USER>/detail","pages/manage/detection/index","pages/manage/detection/result","pages/manage/detection/detail","pages/manage/detection/records","pages/manage/detection/date-list","pages/manage/detection/result-detail","pages/home/<USER>/warning","pages/home/<USER>/rules","pages/home/<USER>/list","pages/home/<USER>/detail","pages/manage/xuncha/list","pages/manage/jianyan/list","pages/manage/chayan/list","pages/manage/taizhang/list","pages/manage/taizhang/detail","pages/manage/pre-code/index","pages/manage/pre-code/bind-list","pages/manage/pre-code/qr-help","pages/manage/pre-code/bind-list2","pages/manage/pre-code/bind-list3","pages/manage/statistics/statistics","pages/manage/cold-storage/cold-storage","pages/manage/cold-storage/add-storage","pages/manage/cold-storage/add-success","pages/manage/maintenance/add","pages/manage/maintenance/add-success","pages/manage/cold-storage/audit-storage","pages/manage/cold-storage/audit-detail","pages/message/index","pages/manage/linshi-car/linshi-car","pages/manage/linshi-car/long-car","pages/manage/linshi-car/car-manage"],"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"uni-app","navigationBarBackgroundColor":"#F8F8F8","backgroundColor":"#F8F8F8","background":"#efeff4"},"tabBar":{"color":"#7382A9","selectedColor":"#0862F6","backgroundColor":"#fff","midButton":{"width":"55px","height":"55px","backgroundImage":"./static/image/tab_scan.png"},"list":[{"pagePath":"pages/home/<USER>/index","text":"首页","iconPath":"./static/image/icon/home.png","selectedIconPath":"./static/image/icon/home_select.png"},{"pagePath":"pages/home/<USER>/index","text":"巡查","iconPath":"./static/image/icon/to_do.png","selectedIconPath":"./static/image/icon/to_do_select.png"},{"pagePath":"pages/manage/index/index","text":"管理","iconPath":"./static/image/icon/manage.png","selectedIconPath":"./static/image/icon/manage_select.png"},{"pagePath":"pages/mine/index/index","text":"我的","iconPath":"./static/image/icon/mine.png","selectedIconPath":"./static/image/icon/mine_select.png"}]},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"uni-app","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":false,"autoclose":false},"appname":"慧市助手","compilerVersion":"4.36","entryPagePath":"pages/login/index","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/login/index","meta":{"isQuit":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/home/<USER>/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/home/<USER>/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/home/<USER>/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/home/<USER>/index","meta":{},"window":{"navigationBarTitleText":"提交成功","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/home/<USER>/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/home/<USER>/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/home/<USER>/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/home/<USER>/index","meta":{},"window":{"navigationBarTitleText":"整改任务","navigationBarBackgroundColor":"#ffffff"}},{"path":"/pages/home/<USER>/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/inspect/index/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/inspect/inspect-progress/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/inspect/record/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/pending/index/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/jingying/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/kaoqin/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/kaoqin/leave","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/week/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/week/record","meta":{},"window":{"navigationBarTitleText":"历史记录","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/manage/week/detail","meta":{},"window":{"navigationBarTitleText":"周排查报告详情","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/manage/month/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/month/record","meta":{},"window":{"navigationBarTitleText":"历史记录","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/manage/month/detail","meta":{},"window":{"navigationBarTitleText":"月调度会议纪要详情","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/manage/year/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/year/record","meta":{},"window":{"navigationBarTitleText":"历史记录","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/manage/year/detail","meta":{},"window":{"navigationBarTitleText":"食品安全年总结报告","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/manage/web-view/index","meta":{},"window":{"navigationBarTitleText":"web-view"}},{"path":"/pages/manage/today-price/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/today-price/choose-products","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/mine/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/mine/user/index","meta":{},"window":{"navigationBarTitleText":"个人资料","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/mine/sign/index","meta":{},"window":{"navigationBarTitleText":"个人签字","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/mine/about/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/mine/agreement/index","meta":{},"window":{"navigationBarTitleText":"","navigationBarBackgroundColor":"#ffffff","navigationBarTextStyle":"black"}},{"path":"/pages/check/index/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/check/manual-check/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/check/check-detail/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/check/check-result/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/check/records-detail/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/check/records-detail/shop-report-list","meta":{},"window":{"navigationBarTitleText":"检测报告","navigationBarBackgroundColor":"#ffffff"}},{"path":"/pages/price/index/index","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"navigationStyle":"custom"}},{"path":"/pages/price/history/history","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"navigationStyle":"custom"}},{"path":"/pages/testing/index/index","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"navigationStyle":"custom"}},{"path":"/pages/testing/history/history","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"navigationStyle":"custom"}},{"path":"/pages/berth/index/index","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"navigationStyle":"custom"}},{"path":"/pages/staff/index/index","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"navigationStyle":"custom"}},{"path":"/pages/staff/records/records","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"navigationStyle":"custom","disableScroll":true}},{"path":"/pages/manage/shop/index","meta":{},"window":{"navigationBarTitleText":"添加店铺","enablePullDownRefresh":false}},{"path":"/pages/home/<USER>/list","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"navigationStyle":"custom"}},{"path":"/pages/home/<USER>/detail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"navigationStyle":"custom"}},{"path":"/pages/manage/detection/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/manage/detection/result","meta":{},"window":{"navigationBarTitleText":"登记结果","enablePullDownRefresh":false}},{"path":"/pages/manage/detection/detail","meta":{},"window":{"navigationBarTitleText":"检测详情","enablePullDownRefresh":false}},{"path":"/pages/manage/detection/records","meta":{},"window":{"navigationBarTitleText":"检测记录","navigationStyle":"custom","enablePullDownRefresh":true}},{"path":"/pages/manage/detection/date-list","meta":{},"window":{"navigationBarTitleText":"历史检测","enablePullDownRefresh":true}},{"path":"/pages/manage/detection/result-detail","meta":{},"window":{"navigationBarTitleText":"检测详情","enablePullDownRefresh":false}},{"path":"/pages/home/<USER>/warning","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":true}},{"path":"/pages/home/<USER>/rules","meta":{},"window":{"navigationBarTitleText":"预警规则","enablePullDownRefresh":false,"navigationBarBackgroundColor":"#fff"}},{"path":"/pages/home/<USER>/list","meta":{},"window":{"navigationBarTitleText":"预警列表","enablePullDownRefresh":true,"navigationBarBackgroundColor":"#fff"}},{"path":"/pages/home/<USER>/detail","meta":{},"window":{"navigationBarTitleText":"详情","enablePullDownRefresh":false}},{"path":"/pages/manage/xuncha/list","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":true}},{"path":"/pages/manage/jianyan/list","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":true}},{"path":"/pages/manage/chayan/list","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":true}},{"path":"/pages/manage/taizhang/list","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":true}},{"path":"/pages/manage/taizhang/detail","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/manage/pre-code/index","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/pre-code/bind-list","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/pre-code/qr-help","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/pre-code/bind-list2","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/pre-code/bind-list3","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/statistics/statistics","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/cold-storage/cold-storage","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/cold-storage/add-storage","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/cold-storage/add-success","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/maintenance/add","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/maintenance/add-success","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/cold-storage/audit-storage","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/cold-storage/audit-detail","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/message/index","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/linshi-car/linshi-car","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/linshi-car/long-car","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}},{"path":"/pages/manage/linshi-car/car-manage","meta":{},"window":{"navigationStyle":"custom","navigationBarTitleText":""}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
