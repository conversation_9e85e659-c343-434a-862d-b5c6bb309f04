<template>
	<canvas id="id" :canvas-id="id" :style="{ width: width + 'px', height: width + 'px'}"></canvas>
</template>

<script>
	import UQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js'
  
	export default {
		props: {
			id: {
				type: String,
				default: 'qrcode'
			},
			width: Number,
			path: String,
			getPath: {
				type: Boolean,
				default: false
			},
			showImg: {
				type: Boolean,
				default: false
			},
			showImgWidth: Number
		},
		mounted() {
			setTimeout(() => {
				var qr = new UQRCode();
				// 设置二维码内容
				qr.data = this.path;
				// 设置二维码大小，必须与canvas设置的宽高一致
				qr.size = this.width;
				// 调用制作二维码方法
				if (this.showImg) {
					qr.foregroundImageSrc = require('@/static/image/logo.png')
					qr.foregroundImageWidth = this.showImgWidth
					qr.foregroundImageHeight = this.showImgWidth
				}
				qr.make();
				// 获取canvas上下文
				var canvasContext = uni.createCanvasContext(this.id, this); // 如果是组件，this必须传入
				// 设置uQRCode实例的canvas上下文
				qr.canvasContext = canvasContext;
				// 调用绘制方法将二维码图案绘制到canvas上
				qr.drawCanvas()
				if (this.getPath) {
					setTimeout(() => {
					    uni.canvasToTempFilePath(
					        {
					            canvasId: this.id,
					            fileType: 'jpg',
					            success: res => {
									this.$emit('getPath', res)
					            },
					            fail: err => {
									uni.showToast({
										title: err,
										icon: 'none'
									})
					            }
					        }, 
					        // this // 组件内使用必传当前实例
					    );
					}, 300)
				}
			}, 200)
		}
	}
</script>

<style>
</style>